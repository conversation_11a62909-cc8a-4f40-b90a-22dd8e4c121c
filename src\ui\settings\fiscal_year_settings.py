# -*- coding: utf-8 -*-
"""
تبويب إعدادات السنة المالية
Fiscal Year Settings Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QDateEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QSpinBox, QCheckBox)
from PySide6.QtCore import Qt, QDate
from PySide6.QtGui import QFont

from ...database.database_manager import DatabaseManager
from ...database.models import FiscalYear
from datetime import datetime, date

class FiscalYearSettingsWidget(QWidget):
    """ويدجت إعدادات السنة المالية"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # مجموعة السنة المالية الحالية
        current_group = QGroupBox("السنة المالية الحالية")
        current_layout = QFormLayout(current_group)
        
        self.current_year_label = QLabel()
        font = QFont()
        font.setBold(True)
        font.setPointSize(12)
        self.current_year_label.setFont(font)
        current_layout.addRow("السنة الحالية:", self.current_year_label)
        
        self.current_start_label = QLabel()
        current_layout.addRow("تاريخ البداية:", self.current_start_label)
        
        self.current_end_label = QLabel()
        current_layout.addRow("تاريخ النهاية:", self.current_end_label)
        
        self.current_status_label = QLabel()
        current_layout.addRow("الحالة:", self.current_status_label)
        
        main_layout.addWidget(current_group)
        
        # مجموعة إضافة سنة مالية جديدة
        new_year_group = QGroupBox("إضافة سنة مالية جديدة")
        new_year_layout = QFormLayout(new_year_group)
        
        self.new_year_spin = QSpinBox()
        self.new_year_spin.setRange(2020, 2050)
        self.new_year_spin.setValue(datetime.now().year + 1)
        new_year_layout.addRow("السنة:", self.new_year_spin)
        
        self.start_date_edit = QDateEdit()
        self.start_date_edit.setDate(QDate(datetime.now().year + 1, 1, 1))
        self.start_date_edit.setCalendarPopup(True)
        new_year_layout.addRow("تاريخ البداية:", self.start_date_edit)
        
        self.end_date_edit = QDateEdit()
        self.end_date_edit.setDate(QDate(datetime.now().year + 1, 12, 31))
        self.end_date_edit.setCalendarPopup(True)
        new_year_layout.addRow("تاريخ النهاية:", self.end_date_edit)
        
        self.make_current_check = QCheckBox("جعلها السنة الحالية")
        new_year_layout.addRow("", self.make_current_check)
        
        # أزرار إدارة السنة المالية
        buttons_layout = QHBoxLayout()
        
        add_button = QPushButton("إضافة سنة مالية")
        add_button.clicked.connect(self.add_fiscal_year)
        add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        close_year_button = QPushButton("إغلاق السنة الحالية")
        close_year_button.clicked.connect(self.close_current_year)
        close_year_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(add_button)
        buttons_layout.addWidget(close_year_button)
        buttons_layout.addStretch()
        
        new_year_layout.addRow("", buttons_layout)
        main_layout.addWidget(new_year_group)
        
        # جدول السنوات المالية
        years_group = QGroupBox("جميع السنوات المالية")
        years_layout = QVBoxLayout(years_group)
        
        self.years_table = QTableWidget()
        self.years_table.setColumnCount(5)
        self.years_table.setHorizontalHeaderLabels([
            "السنة", "تاريخ البداية", "تاريخ النهاية", "الحالة", "مغلقة"
        ])
        
        # تنسيق الجدول
        header = self.years_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.years_table.setAlternatingRowColors(True)
        self.years_table.setSelectionBehavior(QTableWidget.SelectRows)
        
        years_layout.addWidget(self.years_table)
        
        # أزرار إدارة الجدول
        table_buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_data)
        
        delete_button = QPushButton("حذف السنة المحددة")
        delete_button.clicked.connect(self.delete_selected_year)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addStretch()
        
        years_layout.addLayout(table_buttons_layout)
        main_layout.addWidget(years_group)
        
        main_layout.addStretch()
    
    def load_data(self):
        """تحميل بيانات السنوات المالية"""
        session = self.db_manager.get_session()
        try:
            # تحميل السنة المالية الحالية
            current_year = session.query(FiscalYear).filter_by(is_current=True).first()
            if current_year:
                self.current_year_label.setText(str(current_year.year))
                self.current_start_label.setText(current_year.start_date.strftime("%Y/%m/%d"))
                self.current_end_label.setText(current_year.end_date.strftime("%Y/%m/%d"))
                status = "مغلقة" if current_year.is_closed else "مفتوحة"
                self.current_status_label.setText(status)
            else:
                self.current_year_label.setText("غير محددة")
                self.current_start_label.setText("-")
                self.current_end_label.setText("-")
                self.current_status_label.setText("-")
            
            # تحميل جميع السنوات المالية
            all_years = session.query(FiscalYear).order_by(FiscalYear.year.desc()).all()
            
            self.years_table.setRowCount(len(all_years))
            
            for row, year in enumerate(all_years):
                # السنة
                self.years_table.setItem(row, 0, QTableWidgetItem(str(year.year)))
                
                # تاريخ البداية
                start_date = year.start_date.strftime("%Y/%m/%d")
                self.years_table.setItem(row, 1, QTableWidgetItem(start_date))
                
                # تاريخ النهاية
                end_date = year.end_date.strftime("%Y/%m/%d")
                self.years_table.setItem(row, 2, QTableWidgetItem(end_date))
                
                # الحالة
                status = "الحالية" if year.is_current else "غير نشطة"
                status_item = QTableWidgetItem(status)
                if year.is_current:
                    status_item.setBackground(Qt.green)
                self.years_table.setItem(row, 3, status_item)
                
                # مغلقة
                closed_status = "نعم" if year.is_closed else "لا"
                closed_item = QTableWidgetItem(closed_status)
                if year.is_closed:
                    closed_item.setBackground(Qt.red)
                self.years_table.setItem(row, 4, closed_item)
                
                # حفظ ID في البيانات
                self.years_table.item(row, 0).setData(Qt.UserRole, year.id)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل بيانات السنوات المالية:\n{str(e)}"
            )
        finally:
            session.close()
    
    def add_fiscal_year(self):
        """إضافة سنة مالية جديدة"""
        session = self.db_manager.get_session()
        try:
            year = self.new_year_spin.value()
            start_date = self.start_date_edit.date().toPython()
            end_date = self.end_date_edit.date().toPython()
            make_current = self.make_current_check.isChecked()
            
            # التحقق من صحة التواريخ
            if start_date >= end_date:
                QMessageBox.warning(
                    self,
                    "خطأ في التواريخ",
                    "تاريخ البداية يجب أن يكون قبل تاريخ النهاية"
                )
                return
            
            # التحقق من عدم وجود السنة مسبقاً
            existing_year = session.query(FiscalYear).filter_by(year=year).first()
            if existing_year:
                QMessageBox.warning(
                    self,
                    "سنة موجودة",
                    f"السنة المالية {year} موجودة بالفعل"
                )
                return
            
            # إذا كانت ستصبح السنة الحالية، إلغاء تفعيل السنوات الأخرى
            if make_current:
                session.query(FiscalYear).update({FiscalYear.is_current: False})
            
            # إنشاء السنة المالية الجديدة
            new_fiscal_year = FiscalYear(
                year=year,
                start_date=datetime.combine(start_date, datetime.min.time()),
                end_date=datetime.combine(end_date, datetime.min.time()),
                is_current=make_current
            )
            
            session.add(new_fiscal_year)
            session.commit()
            
            QMessageBox.information(
                self,
                "تم الإضافة",
                f"تم إضافة السنة المالية {year} بنجاح"
            )
            
            # تحديث البيانات
            self.load_data()
            
            # إعادة تعيين النموذج
            self.new_year_spin.setValue(year + 1)
            self.start_date_edit.setDate(QDate(year + 1, 1, 1))
            self.end_date_edit.setDate(QDate(year + 1, 12, 31))
            self.make_current_check.setChecked(False)
            
        except Exception as e:
            session.rollback()
            QMessageBox.critical(
                self,
                "خطأ في الإضافة",
                f"حدث خطأ أثناء إضافة السنة المالية:\n{str(e)}"
            )
        finally:
            session.close()
    
    def close_current_year(self):
        """إغلاق السنة المالية الحالية"""
        reply = QMessageBox.question(
            self,
            "تأكيد إغلاق السنة",
            "هل تريد إغلاق السنة المالية الحالية؟\nلن تتمكن من التراجع عن هذا الإجراء.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                current_year = session.query(FiscalYear).filter_by(is_current=True).first()
                if current_year:
                    current_year.is_closed = True
                    session.commit()
                    
                    QMessageBox.information(
                        self,
                        "تم الإغلاق",
                        "تم إغلاق السنة المالية الحالية بنجاح"
                    )
                    
                    self.load_data()
                else:
                    QMessageBox.warning(
                        self,
                        "لا توجد سنة حالية",
                        "لا توجد سنة مالية حالية لإغلاقها"
                    )
                    
            except Exception as e:
                session.rollback()
                QMessageBox.critical(
                    self,
                    "خطأ في الإغلاق",
                    f"حدث خطأ أثناء إغلاق السنة المالية:\n{str(e)}"
                )
            finally:
                session.close()
    
    def delete_selected_year(self):
        """حذف السنة المالية المحددة"""
        current_row = self.years_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(
                self,
                "لا يوجد تحديد",
                "يرجى تحديد سنة مالية للحذف"
            )
            return
        
        year_id = self.years_table.item(current_row, 0).data(Qt.UserRole)
        year_text = self.years_table.item(current_row, 0).text()
        
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف السنة المالية {year_text}؟\nسيتم حذف جميع البيانات المرتبطة بها.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                fiscal_year = session.query(FiscalYear).get(year_id)
                if fiscal_year:
                    if fiscal_year.is_current:
                        QMessageBox.warning(
                            self,
                            "لا يمكن الحذف",
                            "لا يمكن حذف السنة المالية الحالية"
                        )
                        return
                    
                    session.delete(fiscal_year)
                    session.commit()
                    
                    QMessageBox.information(
                        self,
                        "تم الحذف",
                        f"تم حذف السنة المالية {year_text} بنجاح"
                    )
                    
                    self.load_data()
                    
            except Exception as e:
                session.rollback()
                QMessageBox.critical(
                    self,
                    "خطأ في الحذف",
                    f"حدث خطأ أثناء حذف السنة المالية:\n{str(e)}"
                )
            finally:
                session.close()
    
    def save_settings(self):
        """حفظ الإعدادات"""
        # لا توجد إعدادات للحفظ في هذا التبويب
        pass
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        # إعادة تعيين النموذج
        current_year = datetime.now().year
        self.new_year_spin.setValue(current_year + 1)
        self.start_date_edit.setDate(QDate(current_year + 1, 1, 1))
        self.end_date_edit.setDate(QDate(current_year + 1, 12, 31))
        self.make_current_check.setChecked(False)
