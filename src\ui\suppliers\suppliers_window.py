# -*- coding: utf-8 -*-
"""
نافذة إدارة الموردين الرئيسية
Main Suppliers Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTabWidget, QLabel, QPushButton, QFrame, QGridLayout,
                               QMessageBox, QMenuBar, QMenu, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QAction

from .suppliers_data import SuppliersDataWidget
from .suppliers_operations import SuppliersOperationsWidget
from .suppliers_reports import SuppliersReportsWidget

class SuppliersWindow(QMainWindow):
    """نافذة إدارة الموردين الرئيسية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة الموردين - نظام إدارة الشحنات")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        title_label = QLabel("إدارة الموردين والشركاء التجاريين")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # تبويبات إدارة الموردين
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #e67e22;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #f39c12;
                color: white;
            }
        """)
        
        # إضافة التبويبات
        self.setup_tabs()
        
        main_layout.addWidget(self.tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        refresh_button = QPushButton("تحديث البيانات")
        refresh_button.clicked.connect(self.refresh_all_data)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        export_button = QPushButton("تصدير البيانات")
        export_button.clicked.connect(self.export_data)
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.close)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addWidget(export_button)
        buttons_layout.addWidget(close_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_tabs(self):
        """إعداد تبويبات إدارة الموردين"""
        
        # تبويب بيانات الموردين
        self.data_widget = SuppliersDataWidget()
        self.tab_widget.addTab(self.data_widget, "بيانات الموردين")
        
        # تبويب عمليات الموردين
        self.operations_widget = SuppliersOperationsWidget()
        self.tab_widget.addTab(self.operations_widget, "عمليات الموردين")
        
        # تبويب التقارير
        self.reports_widget = SuppliersReportsWidget()
        self.tab_widget.addTab(self.reports_widget, "التقارير")
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        new_supplier_action = QAction("مورد جديد", self)
        new_supplier_action.setShortcut("Ctrl+N")
        new_supplier_action.triggered.connect(self.new_supplier)
        file_menu.addAction(new_supplier_action)
        
        file_menu.addSeparator()
        
        export_action = QAction("تصدير البيانات", self)
        export_action.setShortcut("Ctrl+E")
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)
        
        import_action = QAction("استيراد البيانات", self)
        import_action.setShortcut("Ctrl+I")
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)
        
        file_menu.addSeparator()
        
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة البيانات
        data_menu = menubar.addMenu("البيانات")
        
        refresh_action = QAction("تحديث البيانات", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_all_data)
        data_menu.addAction(refresh_action)
        
        data_menu.addSeparator()
        
        suppliers_action = QAction("إدارة بيانات الموردين", self)
        suppliers_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(0))
        data_menu.addAction(suppliers_action)
        
        operations_action = QAction("عمليات الموردين", self)
        operations_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(1))
        data_menu.addAction(operations_action)
        
        reports_action = QAction("التقارير", self)
        reports_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(2))
        data_menu.addAction(reports_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu("التقارير")
        
        suppliers_report_action = QAction("تقرير الموردين", self)
        suppliers_report_action.triggered.connect(self.generate_suppliers_report)
        reports_menu.addAction(suppliers_report_action)
        
        transactions_report_action = QAction("تقرير المعاملات", self)
        transactions_report_action.triggered.connect(self.generate_transactions_report)
        reports_menu.addAction(transactions_report_action)
        
        performance_report_action = QAction("تقرير الأداء", self)
        performance_report_action.triggered.connect(self.generate_performance_report)
        reports_menu.addAction(performance_report_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("أدوات الموردين")
        
        new_action = QAction("جديد", self)
        new_action.triggered.connect(self.new_supplier)
        toolbar.addAction(new_action)
        
        toolbar.addSeparator()
        
        refresh_action = QAction("تحديث", self)
        refresh_action.triggered.connect(self.refresh_all_data)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        export_action = QAction("تصدير", self)
        export_action.triggered.connect(self.export_data)
        toolbar.addAction(export_action)
    
    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        try:
            # تحديث بيانات كل تبويب
            self.data_widget.load_data()
            self.operations_widget.load_data()
            self.reports_widget.refresh_reports()
            
            QMessageBox.information(
                self,
                "تم التحديث",
                "تم تحديث جميع البيانات بنجاح"
            )
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في التحديث",
                f"حدث خطأ أثناء تحديث البيانات:\n{str(e)}"
            )
    
    def new_supplier(self):
        """إضافة مورد جديد"""
        # التبديل إلى تبويب بيانات الموردين
        self.tab_widget.setCurrentIndex(0)
        # تفعيل وضع الإضافة في تبويب البيانات
        self.data_widget.add_new_supplier()
    
    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة تصدير البيانات قيد التطوير"
        )
    
    def import_data(self):
        """استيراد البيانات"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة استيراد البيانات قيد التطوير"
        )
    
    def generate_suppliers_report(self):
        """إنتاج تقرير الموردين"""
        # التبديل إلى تبويب التقارير
        self.tab_widget.setCurrentIndex(2)
        self.reports_widget.generate_suppliers_report()
    
    def generate_transactions_report(self):
        """إنتاج تقرير المعاملات"""
        # التبديل إلى تبويب التقارير
        self.tab_widget.setCurrentIndex(2)
        self.reports_widget.generate_transactions_report()
    
    def generate_performance_report(self):
        """إنتاج تقرير الأداء"""
        # التبديل إلى تبويب التقارير
        self.tab_widget.setCurrentIndex(2)
        self.reports_widget.generate_performance_report()
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        # يمكن إضافة تحقق من التغييرات غير المحفوظة هنا
        event.accept()
