# -*- coding: utf-8 -*-
"""
نافذة إدارة وحدات القياس
Units Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QMessageBox, QMenuBar, QAction, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from .units_management import UnitsManagementWidget

class UnitsManagementWindow(QMainWindow):
    """نافذة إدارة وحدات القياس"""
    
    data_saved = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة وحدات القياس - نظام إدارة الشحنات")
        self.setMinimumSize(900, 700)
        self.resize(1100, 800)
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        title_label = QLabel("إدارة وحدات القياس")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # ويدجت إدارة وحدات القياس
        self.units_management_widget = UnitsManagementWidget()
        main_layout.addWidget(self.units_management_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        save_button = QPushButton("حفظ التغييرات")
        save_button.clicked.connect(self.save_data)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        refresh_button = QPushButton("تحديث البيانات")
        refresh_button.clicked.connect(self.refresh_data)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.close)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addWidget(close_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_data)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة وحدات القياس
        units_menu = menubar.addMenu("وحدات القياس")
        
        add_unit_action = QAction("إضافة وحدة جديدة", self)
        add_unit_action.triggered.connect(self.add_unit)
        units_menu.addAction(add_unit_action)
        
        edit_unit_action = QAction("تعديل وحدة", self)
        edit_unit_action.triggered.connect(self.edit_unit)
        units_menu.addAction(edit_unit_action)
        
        delete_unit_action = QAction("حذف وحدة", self)
        delete_unit_action.triggered.connect(self.delete_unit)
        units_menu.addAction(delete_unit_action)
        
        units_menu.addSeparator()
        
        import_units_action = QAction("استيراد وحدات", self)
        import_units_action.triggered.connect(self.import_units)
        units_menu.addAction(import_units_action)
        
        export_units_action = QAction("تصدير وحدات", self)
        export_units_action.triggered.connect(self.export_units)
        units_menu.addAction(export_units_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        save_action = QAction("حفظ", self)
        save_action.triggered.connect(self.save_data)
        toolbar.addAction(save_action)
        
        refresh_action = QAction("تحديث", self)
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        add_action = QAction("إضافة وحدة", self)
        add_action.triggered.connect(self.add_unit)
        toolbar.addAction(add_action)
        
        edit_action = QAction("تعديل وحدة", self)
        edit_action.triggered.connect(self.edit_unit)
        toolbar.addAction(edit_action)
        
        delete_action = QAction("حذف وحدة", self)
        delete_action.triggered.connect(self.delete_unit)
        toolbar.addAction(delete_action)
        
        toolbar.addSeparator()
        
        close_action = QAction("إغلاق", self)
        close_action.triggered.connect(self.close)
        toolbar.addAction(close_action)
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            if hasattr(self.units_management_widget, 'save_data'):
                self.units_management_widget.save_data()
            
            QMessageBox.information(self, "تم الحفظ", "تم حفظ بيانات وحدات القياس بنجاح")
            self.data_saved.emit()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}")
    
    def refresh_data(self):
        """تحديث البيانات"""
        try:
            if hasattr(self.units_management_widget, 'load_data'):
                self.units_management_widget.load_data()
            QMessageBox.information(self, "تم التحديث", "تم تحديث بيانات وحدات القياس بنجاح")
        except Exception as e:
            QMessageBox.warning(self, "خطأ في التحديث", f"حدث خطأ أثناء تحديث البيانات:\n{str(e)}")
    
    def add_unit(self):
        """إضافة وحدة جديدة"""
        try:
            if hasattr(self.units_management_widget, 'add_unit'):
                self.units_management_widget.add_unit()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الإضافة", f"حدث خطأ أثناء إضافة الوحدة:\n{str(e)}")
    
    def edit_unit(self):
        """تعديل وحدة"""
        try:
            if hasattr(self.units_management_widget, 'edit_unit'):
                self.units_management_widget.edit_unit()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التعديل", f"حدث خطأ أثناء تعديل الوحدة:\n{str(e)}")
    
    def delete_unit(self):
        """حذف وحدة"""
        try:
            if hasattr(self.units_management_widget, 'delete_unit'):
                self.units_management_widget.delete_unit()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف الوحدة:\n{str(e)}")
    
    def import_units(self):
        """استيراد وحدات"""
        try:
            if hasattr(self.units_management_widget, 'import_units'):
                self.units_management_widget.import_units()
            QMessageBox.information(self, "تم الاستيراد", "تم استيراد وحدات القياس بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الاستيراد", f"حدث خطأ أثناء استيراد الوحدات:\n{str(e)}")
    
    def export_units(self):
        """تصدير وحدات"""
        try:
            if hasattr(self.units_management_widget, 'export_units'):
                self.units_management_widget.export_units()
            QMessageBox.information(self, "تم التصدير", "تم تصدير وحدات القياس بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التصدير", f"حدث خطأ أثناء تصدير الوحدات:\n{str(e)}")
