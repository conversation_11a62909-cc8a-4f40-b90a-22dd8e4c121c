# -*- coding: utf-8 -*-
"""
تبويب المتغيرات العامة
General Settings Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QSpinBox, QCheckBox, QComboBox,
                               QGroupBox, QPushButton, QMessageBox, QTextEdit)
from PySide6.QtCore import Qt

from ...database.database_manager import DatabaseManager

class GeneralSettingsWidget(QWidget):
    """ويدجت المتغيرات العامة"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.setup_ui()
        self.load_settings()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # مجموعة إعدادات التطبيق
        app_group = QGroupBox("إعدادات التطبيق")
        app_layout = QFormLayout(app_group)
        
        self.app_name_edit = QLineEdit()
        app_layout.addRow("اسم التطبيق:", self.app_name_edit)
        
        self.app_version_edit = QLineEdit()
        self.app_version_edit.setReadOnly(True)
        app_layout.addRow("إصدار التطبيق:", self.app_version_edit)
        
        self.language_combo = QComboBox()
        self.language_combo.addItems(["العربية", "English"])
        app_layout.addRow("اللغة:", self.language_combo)
        
        main_layout.addWidget(app_group)
        
        # مجموعة الإعدادات المالية
        financial_group = QGroupBox("الإعدادات المالية")
        financial_layout = QFormLayout(financial_group)
        
        self.default_currency_combo = QComboBox()
        financial_layout.addRow("العملة الافتراضية:", self.default_currency_combo)
        
        self.decimal_places_spin = QSpinBox()
        self.decimal_places_spin.setRange(0, 6)
        self.decimal_places_spin.setValue(2)
        financial_layout.addRow("عدد الخانات العشرية:", self.decimal_places_spin)
        
        self.show_currency_symbol_check = QCheckBox("عرض رمز العملة")
        financial_layout.addRow("", self.show_currency_symbol_check)
        
        main_layout.addWidget(financial_group)
        
        # مجموعة إعدادات التاريخ والوقت
        datetime_group = QGroupBox("إعدادات التاريخ والوقت")
        datetime_layout = QFormLayout(datetime_group)
        
        self.date_format_combo = QComboBox()
        self.date_format_combo.addItems([
            "dd/MM/yyyy",
            "MM/dd/yyyy", 
            "yyyy/MM/dd",
            "dd-MM-yyyy",
            "MM-dd-yyyy",
            "yyyy-MM-dd"
        ])
        datetime_layout.addRow("تنسيق التاريخ:", self.date_format_combo)
        
        self.time_format_combo = QComboBox()
        self.time_format_combo.addItems(["24 ساعة", "12 ساعة"])
        datetime_layout.addRow("تنسيق الوقت:", self.time_format_combo)
        
        main_layout.addWidget(datetime_group)
        
        # مجموعة إعدادات النظام
        system_group = QGroupBox("إعدادات النظام")
        system_layout = QFormLayout(system_group)
        
        self.auto_backup_check = QCheckBox("النسخ الاحتياطي التلقائي")
        system_layout.addRow("", self.auto_backup_check)
        
        self.backup_interval_spin = QSpinBox()
        self.backup_interval_spin.setRange(1, 30)
        self.backup_interval_spin.setValue(7)
        self.backup_interval_spin.setSuffix(" أيام")
        system_layout.addRow("فترة النسخ الاحتياطي:", self.backup_interval_spin)
        
        self.max_backup_files_spin = QSpinBox()
        self.max_backup_files_spin.setRange(1, 100)
        self.max_backup_files_spin.setValue(10)
        system_layout.addRow("عدد ملفات النسخ الاحتياطي:", self.max_backup_files_spin)
        
        main_layout.addWidget(system_group)
        
        # مجموعة إعدادات الواجهة
        ui_group = QGroupBox("إعدادات الواجهة")
        ui_layout = QFormLayout(ui_group)
        
        self.theme_combo = QComboBox()
        self.theme_combo.addItems(["الافتراضي", "داكن", "فاتح"])
        ui_layout.addRow("المظهر:", self.theme_combo)
        
        self.font_size_spin = QSpinBox()
        self.font_size_spin.setRange(8, 20)
        self.font_size_spin.setValue(10)
        ui_layout.addRow("حجم الخط:", self.font_size_spin)
        
        self.show_tooltips_check = QCheckBox("عرض التلميحات")
        ui_layout.addRow("", self.show_tooltips_check)
        
        main_layout.addWidget(ui_group)
        
        main_layout.addStretch()
        
        # تحديث حالة النسخ الاحتياطي
        self.auto_backup_check.toggled.connect(self.backup_interval_spin.setEnabled)
        self.auto_backup_check.toggled.connect(self.max_backup_files_spin.setEnabled)
    
    def load_settings(self):
        """تحميل الإعدادات من قاعدة البيانات"""
        try:
            # إعدادات التطبيق
            app_name = self.db_manager.get_setting("app_name", "نظام إدارة الشحنات")
            self.app_name_edit.setText(app_name)
            
            app_version = self.db_manager.get_setting("app_version", "1.0.0")
            self.app_version_edit.setText(app_version)
            
            # الإعدادات المالية
            decimal_places = int(self.db_manager.get_setting("decimal_places", "2"))
            self.decimal_places_spin.setValue(decimal_places)
            
            show_currency = self.db_manager.get_setting("show_currency_symbol", "true") == "true"
            self.show_currency_symbol_check.setChecked(show_currency)
            
            # إعدادات التاريخ
            date_format = self.db_manager.get_setting("date_format", "dd/MM/yyyy")
            index = self.date_format_combo.findText(date_format)
            if index >= 0:
                self.date_format_combo.setCurrentIndex(index)
            
            # إعدادات النظام
            auto_backup = self.db_manager.get_setting("backup_enabled", "true") == "true"
            self.auto_backup_check.setChecked(auto_backup)
            
            backup_interval = int(self.db_manager.get_setting("backup_interval", "7"))
            self.backup_interval_spin.setValue(backup_interval)
            
            max_backups = int(self.db_manager.get_setting("max_backup_files", "10"))
            self.max_backup_files_spin.setValue(max_backups)
            
            # إعدادات الواجهة
            font_size = int(self.db_manager.get_setting("font_size", "10"))
            self.font_size_spin.setValue(font_size)
            
            show_tooltips = self.db_manager.get_setting("show_tooltips", "true") == "true"
            self.show_tooltips_check.setChecked(show_tooltips)
            
            # تحديث حالة النسخ الاحتياطي
            self.backup_interval_spin.setEnabled(auto_backup)
            self.max_backup_files_spin.setEnabled(auto_backup)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل الإعدادات",
                f"حدث خطأ أثناء تحميل الإعدادات:\n{str(e)}"
            )
    
    def save_settings(self):
        """حفظ الإعدادات في قاعدة البيانات"""
        try:
            # حفظ إعدادات التطبيق
            self.db_manager.set_setting("app_name", self.app_name_edit.text())
            
            # حفظ الإعدادات المالية
            self.db_manager.set_setting("decimal_places", str(self.decimal_places_spin.value()))
            self.db_manager.set_setting("show_currency_symbol", 
                                      "true" if self.show_currency_symbol_check.isChecked() else "false")
            
            # حفظ إعدادات التاريخ
            self.db_manager.set_setting("date_format", self.date_format_combo.currentText())
            
            # حفظ إعدادات النظام
            self.db_manager.set_setting("backup_enabled", 
                                      "true" if self.auto_backup_check.isChecked() else "false")
            self.db_manager.set_setting("backup_interval", str(self.backup_interval_spin.value()))
            self.db_manager.set_setting("max_backup_files", str(self.max_backup_files_spin.value()))
            
            # حفظ إعدادات الواجهة
            self.db_manager.set_setting("font_size", str(self.font_size_spin.value()))
            self.db_manager.set_setting("show_tooltips", 
                                      "true" if self.show_tooltips_check.isChecked() else "false")
            
        except Exception as e:
            raise Exception(f"خطأ في حفظ المتغيرات العامة: {str(e)}")
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        self.app_name_edit.setText("نظام إدارة الشحنات")
        self.decimal_places_spin.setValue(2)
        self.show_currency_symbol_check.setChecked(True)
        self.date_format_combo.setCurrentText("dd/MM/yyyy")
        self.auto_backup_check.setChecked(True)
        self.backup_interval_spin.setValue(7)
        self.max_backup_files_spin.setValue(10)
        self.font_size_spin.setValue(10)
        self.show_tooltips_check.setChecked(True)
