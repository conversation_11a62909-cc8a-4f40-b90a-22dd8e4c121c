# -*- coding: utf-8 -*-
"""
نافذة إعدادات المستخدمين
User Settings Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QMessageBox, QMenuBar, QAction, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from .user_settings import UserSettingsWidget

class UserSettingsWindow(QMainWindow):
    """نافذة إعدادات المستخدمين"""
    
    settings_saved = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إعدادات المستخدمين - نظام إدارة الشحنات")
        self.setMinimumSize(900, 700)
        self.resize(1100, 800)
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        title_label = QLabel("إدارة المستخدمين والحسابات")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # ويدجت إعدادات المستخدمين
        self.user_settings_widget = UserSettingsWidget()
        main_layout.addWidget(self.user_settings_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        save_button = QPushButton("حفظ الإعدادات")
        save_button.clicked.connect(self.save_settings)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        apply_button = QPushButton("تطبيق")
        apply_button.clicked.connect(self.apply_settings)
        apply_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.close)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(apply_button)
        buttons_layout.addWidget(cancel_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_settings)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة المستخدمين
        users_menu = menubar.addMenu("المستخدمين")
        add_user_action = QAction("إضافة مستخدم", self)
        add_user_action.triggered.connect(self.add_user)
        users_menu.addAction(add_user_action)
        
        edit_user_action = QAction("تعديل مستخدم", self)
        edit_user_action.triggered.connect(self.edit_user)
        users_menu.addAction(edit_user_action)
        
        delete_user_action = QAction("حذف مستخدم", self)
        delete_user_action.triggered.connect(self.delete_user)
        users_menu.addAction(delete_user_action)
        
        users_menu.addSeparator()
        
        change_password_action = QAction("تغيير كلمة المرور", self)
        change_password_action.triggered.connect(self.change_password)
        users_menu.addAction(change_password_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        save_action = QAction("حفظ", self)
        save_action.triggered.connect(self.save_settings)
        toolbar.addAction(save_action)
        
        apply_action = QAction("تطبيق", self)
        apply_action.triggered.connect(self.apply_settings)
        toolbar.addAction(apply_action)
        
        toolbar.addSeparator()
        
        add_user_action = QAction("إضافة مستخدم", self)
        add_user_action.triggered.connect(self.add_user)
        toolbar.addAction(add_user_action)
        
        edit_user_action = QAction("تعديل", self)
        edit_user_action.triggered.connect(self.edit_user)
        toolbar.addAction(edit_user_action)
        
        delete_user_action = QAction("حذف", self)
        delete_user_action.triggered.connect(self.delete_user)
        toolbar.addAction(delete_user_action)
        
        toolbar.addSeparator()
        
        close_action = QAction("إغلاق", self)
        close_action.triggered.connect(self.close)
        toolbar.addAction(close_action)
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            if hasattr(self.user_settings_widget, 'save_settings'):
                self.user_settings_widget.save_settings()
            
            QMessageBox.information(self, "تم الحفظ", "تم حفظ إعدادات المستخدمين بنجاح")
            self.settings_saved.emit()
            self.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"حدث خطأ أثناء حفظ إعدادات المستخدمين:\n{str(e)}")
    
    def apply_settings(self):
        """تطبيق الإعدادات"""
        try:
            if hasattr(self.user_settings_widget, 'save_settings'):
                self.user_settings_widget.save_settings()
            
            QMessageBox.information(self, "تم التطبيق", "تم تطبيق إعدادات المستخدمين بنجاح")
            self.settings_saved.emit()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التطبيق", f"حدث خطأ أثناء تطبيق إعدادات المستخدمين:\n{str(e)}")
    
    def add_user(self):
        """إضافة مستخدم جديد"""
        try:
            if hasattr(self.user_settings_widget, 'add_user'):
                self.user_settings_widget.add_user()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الإضافة", f"حدث خطأ أثناء إضافة المستخدم:\n{str(e)}")
    
    def edit_user(self):
        """تعديل مستخدم"""
        try:
            if hasattr(self.user_settings_widget, 'edit_user'):
                self.user_settings_widget.edit_user()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التعديل", f"حدث خطأ أثناء تعديل المستخدم:\n{str(e)}")
    
    def delete_user(self):
        """حذف مستخدم"""
        try:
            if hasattr(self.user_settings_widget, 'delete_user'):
                self.user_settings_widget.delete_user()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف المستخدم:\n{str(e)}")
    
    def change_password(self):
        """تغيير كلمة المرور"""
        try:
            if hasattr(self.user_settings_widget, 'change_password'):
                self.user_settings_widget.change_password()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في تغيير كلمة المرور", f"حدث خطأ أثناء تغيير كلمة المرور:\n{str(e)}")
    
    def refresh_data(self):
        """تحديث البيانات"""
        if hasattr(self.user_settings_widget, 'load_settings'):
            self.user_settings_widget.load_settings()
