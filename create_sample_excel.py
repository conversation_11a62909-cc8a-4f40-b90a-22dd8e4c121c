#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd

# إنشاء بيانات نموذجية للأصناف مع الحقول الجديدة
data = {
    'اسم الصنف': [
        'أرز بسمتي',
        'زيت زيتون',
        'سكر أبيض',
        'دقيق أبيض',
        'شاي أحمر'
    ],
    'كود الصنف': [
        'RICE001',
        'OIL001',
        'SUGAR001',
        'FLOUR001',
        'TEA001'
    ],
    'الاسم الإنجليزي': [
        'Basmati Rice',
        'Olive Oil',
        'White Sugar',
        'White Flour',
        'Black Tea'
    ],
    'الوصف': [
        'أرز بسمتي فاخر من الهند',
        'زيت زيتون بكر ممتاز',
        'سكر أبيض ناعم',
        'دقيق أبيض فاخر',
        'شاي أحمر سيلاني'
    ],
    'المجموعة': [
        'الحبوب',
        'الزيوت',
        'السكريات',
        'الدقيق',
        'المشروبات'
    ],
    'وحدة القياس': [
        'كيلو',
        'لتر',
        'كيلو',
        'كيلو',
        'علبة'
    ],
    'سعر التكلفة': [
        15.50,
        45.00,
        8.75,
        12.25,
        25.00
    ],
    'سعر البيع': [
        18.00,
        55.00,
        10.50,
        15.00,
        30.00
    ],
    'الوزن': [
        1.0,
        0.5,
        1.0,
        1.0,
        0.25
    ],
    'الأبعاد': [
        '30x20x5',
        '25x8x8',
        '25x15x8',
        '35x25x10',
        '15x10x8'
    ],
    'عبوة الكلي': [
        20,
        12,
        25,
        15,
        24
    ],
    'عبوة الجزئي': [
        1,
        1,
        1,
        1,
        1
    ],
    'الجرام': [
        1000,
        500,
        1000,
        1000,
        250
    ],
    'وزن الصنف': [
        1.0,
        0.5,
        1.0,
        1.0,
        0.25
    ]
}

# إنشاء DataFrame
df = pd.DataFrame(data)

# حفظ الملف
df.to_excel('نموذج_استيراد_الأصناف_محدث.xlsx', index=False, engine='openpyxl')

print("تم إنشاء ملف الإكسيل النموذجي بنجاح!")
print("اسم الملف: نموذج_استيراد_الأصناف_محدث.xlsx")
print("\nالأعمدة المتاحة:")
for col in df.columns:
    print(f"- {col}")
