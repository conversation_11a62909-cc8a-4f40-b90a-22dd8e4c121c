# -*- coding: utf-8 -*-
"""
إنشاء ملف إكسيل نموذجي لاستيراد الأصناف
Create sample Excel file for items import
"""

import pandas as pd

# إنشاء بيانات نموذجية للأصناف
sample_data = {
    'اسم الصنف': [
        'لابتوب ديل',
        'ماوس لاسلكي',
        'لوحة مفاتيح',
        'شاشة 24 بوصة',
        'طابعة ليزر'
    ],
    'الاسم الإنجليزي': [
        'Dell Laptop',
        'Wireless Mouse',
        'Keyboard',
        '24 inch Monitor',
        'Laser Printer'
    ],
    'كود الصنف': [
        'LAP001',
        'MOU001',
        'KEY001',
        'MON001',
        'PRI001'
    ],
    'الوصف': [
        'لابتوب ديل انسبايرون 15',
        'ماوس لاسلكي بتقنية البلوتوث',
        'لوحة مفاتيح عربي/إنجليزي',
        'شاشة LED عالية الدقة',
        'طابعة ليزر أبيض وأسود'
    ],
    'المجموعة': [
        'أجهزة كمبيوتر',
        'ملحقات كمبيوتر',
        'ملحقات كمبيوتر',
        'أجهزة كمبيوتر',
        'أجهزة كمبيوتر'
    ],
    'وحدة القياس': [
        'قطعة',
        'قطعة',
        'قطعة',
        'قطعة',
        'قطعة'
    ],
    'سعر التكلفة': [
        2500.00,
        45.00,
        120.00,
        800.00,
        1200.00
    ],
    'سعر البيع': [
        3000.00,
        65.00,
        150.00,
        950.00,
        1500.00
    ],
    'الوزن': [
        2.5,
        0.1,
        0.8,
        4.2,
        8.5
    ],
    'الأبعاد': [
        '35x25x2 سم',
        '10x6x3 سم',
        '45x15x3 سم',
        '55x35x8 سم',
        '40x30x25 سم'
    ]
}

# إنشاء DataFrame
df = pd.DataFrame(sample_data)

# حفظ الملف
df.to_excel('نموذج_استيراد_الأصناف.xlsx', index=False, engine='openpyxl')

print("تم إنشاء ملف نموذج_استيراد_الأصناف.xlsx بنجاح!")
print("\nالأعمدة المطلوبة:")
print("- اسم الصنف (مطلوب)")
print("- كود الصنف (مطلوب)")
print("- الاسم الإنجليزي (اختياري)")
print("- الوصف (اختياري)")
print("- المجموعة (اختياري - يجب أن تكون موجودة مسبقاً)")
print("- وحدة القياس (اختياري - يجب أن تكون موجودة مسبقاً)")
print("- سعر التكلفة (اختياري)")
print("- سعر البيع (اختياري)")
print("- الوزن (اختياري)")
print("- الأبعاد (اختياري)")
