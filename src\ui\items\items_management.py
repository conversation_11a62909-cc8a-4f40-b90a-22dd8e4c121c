# -*- coding: utf-8 -*-
"""
ويدجت إدارة الأصناف
Items Management Widget
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QTextEdit, QCheckBox,
                               QComboBox, QSpinBox, QDoubleSpinBox)
from PySide6.QtCore import Qt

from ...database.database_manager import DatabaseManager
from ...database.models import Item, ItemGroup, UnitOfMeasure

class ItemsManagementWidget(QWidget):
    """ويدجت إدارة الأصناف"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_item_id = None
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(15)
        
        # الجانب الأيسر - نموذج الإدخال
        form_group = QGroupBox("بيانات الصنف")
        form_group.setMaximumWidth(450)
        form_layout = QVBoxLayout(form_group)
        
        # البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_layout = QFormLayout(basic_group)
        
        self.item_name_edit = QLineEdit()
        self.item_name_edit.setPlaceholderText("اسم الصنف")
        basic_layout.addRow("اسم الصنف:", self.item_name_edit)
        
        self.item_code_edit = QLineEdit()
        self.item_code_edit.setPlaceholderText("كود الصنف")
        basic_layout.addRow("كود الصنف:", self.item_code_edit)
        
        self.barcode_edit = QLineEdit()
        self.barcode_edit.setPlaceholderText("الباركود")
        basic_layout.addRow("الباركود:", self.barcode_edit)
        
        self.group_combo = QComboBox()
        self.load_item_groups()
        basic_layout.addRow("مجموعة الصنف:", self.group_combo)
        
        self.unit_combo = QComboBox()
        self.load_units()
        basic_layout.addRow("وحدة القياس:", self.unit_combo)
        
        form_layout.addWidget(basic_group)
        
        # الأسعار والتكاليف
        pricing_group = QGroupBox("الأسعار والتكاليف")
        pricing_layout = QFormLayout(pricing_group)
        
        self.cost_price_edit = QDoubleSpinBox()
        self.cost_price_edit.setMaximum(999999.99)
        self.cost_price_edit.setSuffix(" ريال")
        self.cost_price_edit.setDecimals(2)
        pricing_layout.addRow("سعر التكلفة:", self.cost_price_edit)
        
        self.selling_price_edit = QDoubleSpinBox()
        self.selling_price_edit.setMaximum(999999.99)
        self.selling_price_edit.setSuffix(" ريال")
        self.selling_price_edit.setDecimals(2)
        pricing_layout.addRow("سعر البيع:", self.selling_price_edit)
        
        self.wholesale_price_edit = QDoubleSpinBox()
        self.wholesale_price_edit.setMaximum(999999.99)
        self.wholesale_price_edit.setSuffix(" ريال")
        self.wholesale_price_edit.setDecimals(2)
        pricing_layout.addRow("سعر الجملة:", self.wholesale_price_edit)
        
        form_layout.addWidget(pricing_group)
        
        # المخزون
        inventory_group = QGroupBox("المخزون")
        inventory_layout = QFormLayout(inventory_group)
        
        self.current_stock_edit = QSpinBox()
        self.current_stock_edit.setMaximum(999999)
        inventory_layout.addRow("الكمية الحالية:", self.current_stock_edit)
        
        self.min_stock_edit = QSpinBox()
        self.min_stock_edit.setMaximum(999999)
        inventory_layout.addRow("الحد الأدنى:", self.min_stock_edit)
        
        self.max_stock_edit = QSpinBox()
        self.max_stock_edit.setMaximum(999999)
        inventory_layout.addRow("الحد الأقصى:", self.max_stock_edit)
        
        form_layout.addWidget(inventory_group)
        
        # معلومات إضافية
        additional_group = QGroupBox("معلومات إضافية")
        additional_layout = QFormLayout(additional_group)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(60)
        self.description_edit.setPlaceholderText("وصف الصنف")
        additional_layout.addRow("الوصف:", self.description_edit)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(60)
        self.notes_edit.setPlaceholderText("ملاحظات")
        additional_layout.addRow("ملاحظات:", self.notes_edit)
        
        self.is_active_check = QCheckBox("نشط")
        self.is_active_check.setChecked(True)
        additional_layout.addRow("", self.is_active_check)
        
        form_layout.addWidget(additional_group)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton("إضافة")
        self.add_button.clicked.connect(self.add_item)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.update_button = QPushButton("تحديث")
        self.update_button.clicked.connect(self.update_item)
        self.update_button.setEnabled(False)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        
        self.clear_button = QPushButton("مسح")
        self.clear_button.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.clear_button)
        
        form_layout.addLayout(buttons_layout)
        
        main_layout.addWidget(form_group)
        
        # الجانب الأيمن - جدول الأصناف
        table_group = QGroupBox("الأصناف المسجلة")
        table_layout = QVBoxLayout(table_group)
        
        # فلاتر البحث
        search_layout = QHBoxLayout()
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في الأصناف...")
        self.search_edit.textChanged.connect(self.filter_items)
        
        self.group_filter_combo = QComboBox()
        self.group_filter_combo.addItem("جميع المجموعات", None)
        self.load_groups_for_filter()
        self.group_filter_combo.currentTextChanged.connect(self.filter_items)
        
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(QLabel("المجموعة:"))
        search_layout.addWidget(self.group_filter_combo)
        
        table_layout.addLayout(search_layout)
        
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(8)
        self.items_table.setHorizontalHeaderLabels([
            "اسم الصنف", "الكود", "المجموعة", "الوحدة", "سعر البيع", 
            "الكمية الحالية", "الحد الأدنى", "الحالة"
        ])
        
        # تنسيق الجدول
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.items_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        table_layout.addWidget(self.items_table)
        
        # أزرار إدارة الجدول
        table_buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_data)
        
        edit_button = QPushButton("تعديل المحدد")
        edit_button.clicked.connect(self.edit_selected)
        
        delete_button = QPushButton("حذف المحدد")
        delete_button.clicked.connect(self.delete_selected)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(edit_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addStretch()
        
        table_layout.addLayout(table_buttons_layout)
        main_layout.addWidget(table_group)
