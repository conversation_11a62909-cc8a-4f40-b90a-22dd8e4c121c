# -*- coding: utf-8 -*-
"""
ويدجت إدارة الأصناف
Items Management Widget
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QTextEdit, QCheckBox,
                               QComboBox, QSpinBox, QDoubleSpinBox)
from PySide6.QtCore import Qt

from ...database.database_manager import DatabaseManager
from ...database.models import Item, ItemGroup, UnitOfMeasure

class ItemsManagementWidget(QWidget):
    """ويدجت إدارة الأصناف"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_item_id = None
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QHBoxLayout(self)
        main_layout.setSpacing(15)
        
        # الجانب الأيسر - نموذج الإدخال
        form_group = QGroupBox("بيانات الصنف")
        form_group.setMaximumWidth(450)
        form_layout = QVBoxLayout(form_group)
        
        # البيانات الأساسية
        basic_group = QGroupBox("البيانات الأساسية")
        basic_layout = QFormLayout(basic_group)
        
        self.item_name_edit = QLineEdit()
        self.item_name_edit.setPlaceholderText("اسم الصنف")
        basic_layout.addRow("اسم الصنف:", self.item_name_edit)
        
        self.item_code_edit = QLineEdit()
        self.item_code_edit.setPlaceholderText("كود الصنف")
        basic_layout.addRow("كود الصنف:", self.item_code_edit)
        
        self.barcode_edit = QLineEdit()
        self.barcode_edit.setPlaceholderText("الباركود")
        basic_layout.addRow("الباركود:", self.barcode_edit)
        
        self.group_combo = QComboBox()
        self.load_item_groups()
        basic_layout.addRow("مجموعة الصنف:", self.group_combo)
        
        self.unit_combo = QComboBox()
        self.load_units()
        basic_layout.addRow("وحدة القياس:", self.unit_combo)
        
        form_layout.addWidget(basic_group)
        
        # الأسعار والتكاليف
        pricing_group = QGroupBox("الأسعار والتكاليف")
        pricing_layout = QFormLayout(pricing_group)
        
        self.cost_price_edit = QDoubleSpinBox()
        self.cost_price_edit.setMaximum(999999.99)
        self.cost_price_edit.setSuffix(" ريال")
        self.cost_price_edit.setDecimals(2)
        pricing_layout.addRow("سعر التكلفة:", self.cost_price_edit)
        
        self.selling_price_edit = QDoubleSpinBox()
        self.selling_price_edit.setMaximum(999999.99)
        self.selling_price_edit.setSuffix(" ريال")
        self.selling_price_edit.setDecimals(2)
        pricing_layout.addRow("سعر البيع:", self.selling_price_edit)
        
        self.wholesale_price_edit = QDoubleSpinBox()
        self.wholesale_price_edit.setMaximum(999999.99)
        self.wholesale_price_edit.setSuffix(" ريال")
        self.wholesale_price_edit.setDecimals(2)
        pricing_layout.addRow("سعر الجملة:", self.wholesale_price_edit)
        
        form_layout.addWidget(pricing_group)
        
        # المخزون
        inventory_group = QGroupBox("المخزون")
        inventory_layout = QFormLayout(inventory_group)
        
        self.current_stock_edit = QSpinBox()
        self.current_stock_edit.setMaximum(999999)
        inventory_layout.addRow("الكمية الحالية:", self.current_stock_edit)
        
        self.min_stock_edit = QSpinBox()
        self.min_stock_edit.setMaximum(999999)
        inventory_layout.addRow("الحد الأدنى:", self.min_stock_edit)
        
        self.max_stock_edit = QSpinBox()
        self.max_stock_edit.setMaximum(999999)
        inventory_layout.addRow("الحد الأقصى:", self.max_stock_edit)
        
        form_layout.addWidget(inventory_group)
        
        # معلومات إضافية
        additional_group = QGroupBox("معلومات إضافية")
        additional_layout = QFormLayout(additional_group)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(60)
        self.description_edit.setPlaceholderText("وصف الصنف")
        additional_layout.addRow("الوصف:", self.description_edit)
        
        self.notes_edit = QTextEdit()
        self.notes_edit.setMaximumHeight(60)
        self.notes_edit.setPlaceholderText("ملاحظات")
        additional_layout.addRow("ملاحظات:", self.notes_edit)
        
        self.is_active_check = QCheckBox("نشط")
        self.is_active_check.setChecked(True)
        additional_layout.addRow("", self.is_active_check)
        
        form_layout.addWidget(additional_group)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton("إضافة")
        self.add_button.clicked.connect(self.add_item)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.update_button = QPushButton("تحديث")
        self.update_button.clicked.connect(self.update_item)
        self.update_button.setEnabled(False)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        
        self.clear_button = QPushButton("مسح")
        self.clear_button.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.clear_button)
        
        form_layout.addLayout(buttons_layout)
        
        main_layout.addWidget(form_group)
        
        # الجانب الأيمن - جدول الأصناف
        table_group = QGroupBox("الأصناف المسجلة")
        table_layout = QVBoxLayout(table_group)
        
        # فلاتر البحث
        search_layout = QHBoxLayout()
        
        self.search_edit = QLineEdit()
        self.search_edit.setPlaceholderText("البحث في الأصناف...")
        self.search_edit.textChanged.connect(self.filter_items)
        
        self.group_filter_combo = QComboBox()
        self.group_filter_combo.addItem("جميع المجموعات", None)
        self.load_groups_for_filter()
        self.group_filter_combo.currentTextChanged.connect(self.filter_items)
        
        search_layout.addWidget(QLabel("البحث:"))
        search_layout.addWidget(self.search_edit)
        search_layout.addWidget(QLabel("المجموعة:"))
        search_layout.addWidget(self.group_filter_combo)
        
        table_layout.addLayout(search_layout)
        
        self.items_table = QTableWidget()
        self.items_table.setColumnCount(8)
        self.items_table.setHorizontalHeaderLabels([
            "اسم الصنف", "الكود", "المجموعة", "الوحدة", "سعر البيع", 
            "الكمية الحالية", "الحد الأدنى", "الحالة"
        ])
        
        # تنسيق الجدول
        header = self.items_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.items_table.setAlternatingRowColors(True)
        self.items_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.items_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        table_layout.addWidget(self.items_table)
        
        # أزرار إدارة الجدول
        table_buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_data)
        
        edit_button = QPushButton("تعديل المحدد")
        edit_button.clicked.connect(self.edit_selected)
        
        delete_button = QPushButton("حذف المحدد")
        delete_button.clicked.connect(self.delete_selected)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(edit_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addStretch()
        
        table_layout.addLayout(table_buttons_layout)
        main_layout.addWidget(table_group)

        # تحميل البيانات الأولية
        self.load_data()

    def load_item_groups(self):
        """تحميل مجموعات الأصناف"""
        session = self.db_manager.get_session()
        try:
            groups = session.query(ItemGroup).filter_by(is_active=True).all()
            self.group_combo.clear()
            self.group_combo.addItem("-- اختر المجموعة --", None)
            for group in groups:
                self.group_combo.addItem(group.name, group.id)
        except Exception as e:
            print(f"خطأ في تحميل مجموعات الأصناف: {e}")
        finally:
            session.close()

    def load_units(self):
        """تحميل وحدات القياس"""
        session = self.db_manager.get_session()
        try:
            units = session.query(UnitOfMeasure).filter_by(is_active=True).all()
            self.unit_combo.clear()
            self.unit_combo.addItem("-- اختر الوحدة --", None)
            for unit in units:
                self.unit_combo.addItem(unit.name, unit.id)
        except Exception as e:
            print(f"خطأ في تحميل وحدات القياس: {e}")
        finally:
            session.close()

    def load_data(self):
        """تحميل بيانات الأصناف"""
        session = self.db_manager.get_session()
        try:
            items = session.query(Item).filter_by(is_active=True).all()

            self.items_table.setRowCount(len(items))

            for row, item in enumerate(items):
                self.items_table.setItem(row, 0, QTableWidgetItem(item.name or ""))
                self.items_table.setItem(row, 1, QTableWidgetItem(item.code or ""))

                # مجموعة الصنف
                group_name = item.group.name if item.group else "غير محدد"
                self.items_table.setItem(row, 2, QTableWidgetItem(group_name))

                # وحدة القياس
                unit_name = item.unit.name if item.unit else "غير محدد"
                self.items_table.setItem(row, 3, QTableWidgetItem(unit_name))

                # سعر البيع
                selling_price = f"{item.selling_price:.2f}" if item.selling_price else "0.00"
                self.items_table.setItem(row, 4, QTableWidgetItem(selling_price))

                # الكمية الحالية (افتراضية)
                self.items_table.setItem(row, 5, QTableWidgetItem("0"))

                # الحد الأدنى (افتراضي)
                self.items_table.setItem(row, 6, QTableWidgetItem("0"))

                # الحالة
                status = "نشط" if item.is_active else "غير نشط"
                self.items_table.setItem(row, 7, QTableWidgetItem(status))

                # حفظ معرف الصنف
                self.items_table.item(row, 0).setData(Qt.UserRole, item.id)

        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل بيانات الأصناف:\n{str(e)}"
            )
        finally:
            session.close()

    def validate_form(self):
        """التحقق من صحة البيانات"""
        if not self.name_edit.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال اسم الصنف")
            return False

        if not self.code_edit.text().strip():
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى إدخال كود الصنف")
            return False

        if self.group_combo.currentData() is None:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى اختيار مجموعة الصنف")
            return False

        if self.unit_combo.currentData() is None:
            QMessageBox.warning(self, "بيانات ناقصة", "يرجى اختيار وحدة القياس")
            return False

        return True

    def add_item(self):
        """إضافة صنف جديد"""
        if not self.validate_form():
            return

        session = self.db_manager.get_session()
        try:
            # التحقق من عدم وجود الكود مسبقاً
            existing = session.query(Item).filter_by(code=self.code_edit.text()).first()
            if existing:
                QMessageBox.warning(self, "كود موجود", "هذا الكود موجود بالفعل")
                return

            # إنشاء الصنف الجديد
            new_item = Item(
                name=self.name_edit.text(),
                name_en=self.name_en_edit.text(),
                code=self.code_edit.text(),
                description=self.description_edit.toPlainText(),
                group_id=self.group_combo.currentData(),
                unit_id=self.unit_combo.currentData(),
                cost_price=self.cost_price_spin.value(),
                selling_price=self.selling_price_spin.value(),
                weight=self.weight_spin.value() if self.weight_spin.value() > 0 else None,
                dimensions=self.dimensions_edit.text() if self.dimensions_edit.text().strip() else None,
                is_active=self.is_active_check.isChecked()
            )

            session.add(new_item)
            session.commit()

            QMessageBox.information(self, "تم الإضافة", "تم إضافة الصنف بنجاح")

            self.clear_form()
            self.load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في الإضافة", f"حدث خطأ أثناء إضافة الصنف:\n{str(e)}")
        finally:
            session.close()

    def update_item(self):
        """تحديث الصنف"""
        if not self.current_item_id or not self.validate_form():
            return

        session = self.db_manager.get_session()
        try:
            item = session.query(Item).get(self.current_item_id)
            if item:
                item.name = self.name_edit.text()
                item.name_en = self.name_en_edit.text()
                item.code = self.code_edit.text()
                item.description = self.description_edit.toPlainText()
                item.group_id = self.group_combo.currentData()
                item.unit_id = self.unit_combo.currentData()
                item.cost_price = self.cost_price_spin.value()
                item.selling_price = self.selling_price_spin.value()
                item.weight = self.weight_spin.value() if self.weight_spin.value() > 0 else None
                item.dimensions = self.dimensions_edit.text() if self.dimensions_edit.text().strip() else None
                item.is_active = self.is_active_check.isChecked()

                session.commit()

                QMessageBox.information(self, "تم التحديث", "تم تحديث الصنف بنجاح")

                self.clear_form()
                self.load_data()

        except Exception as e:
            session.rollback()
            QMessageBox.critical(self, "خطأ في التحديث", f"حدث خطأ أثناء تحديث الصنف:\n{str(e)}")
        finally:
            session.close()

    def clear_form(self):
        """مسح النموذج"""
        self.current_item_id = None
        self.name_edit.clear()
        self.name_en_edit.clear()
        self.code_edit.clear()
        self.description_edit.clear()
        self.group_combo.setCurrentIndex(0)
        self.unit_combo.setCurrentIndex(0)
        self.cost_price_spin.setValue(0.0)
        self.selling_price_spin.setValue(0.0)
        self.weight_spin.setValue(0.0)
        self.dimensions_edit.clear()
        self.is_active_check.setChecked(True)

        self.add_button.setEnabled(True)
        self.update_button.setEnabled(False)

    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.items_table.currentRow()
        if current_row >= 0:
            self.edit_selected()

    def edit_selected(self):
        """تعديل الصنف المحدد"""
        current_row = self.items_table.currentRow()
        if current_row < 0:
            return

        item_id = self.items_table.item(current_row, 0).data(Qt.UserRole)

        session = self.db_manager.get_session()
        try:
            item = session.query(Item).get(item_id)
            if item:
                self.current_item_id = item.id
                self.name_edit.setText(item.name or "")
                self.name_en_edit.setText(item.name_en or "")
                self.code_edit.setText(item.code or "")
                self.description_edit.setPlainText(item.description or "")

                # تحديد المجموعة
                for i in range(self.group_combo.count()):
                    if self.group_combo.itemData(i) == item.group_id:
                        self.group_combo.setCurrentIndex(i)
                        break

                # تحديد الوحدة
                for i in range(self.unit_combo.count()):
                    if self.unit_combo.itemData(i) == item.unit_id:
                        self.unit_combo.setCurrentIndex(i)
                        break

                self.cost_price_spin.setValue(item.cost_price or 0.0)
                self.selling_price_spin.setValue(item.selling_price or 0.0)
                self.weight_spin.setValue(item.weight or 0.0)
                self.dimensions_edit.setText(item.dimensions or "")
                self.is_active_check.setChecked(item.is_active)

                self.add_button.setEnabled(False)
                self.update_button.setEnabled(True)

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء تحميل بيانات الصنف:\n{str(e)}")
        finally:
            session.close()

    def delete_selected(self):
        """حذف الصنف المحدد"""
        current_row = self.items_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "لا يوجد تحديد", "يرجى تحديد صنف للحذف")
            return

        item_id = self.items_table.item(current_row, 0).data(Qt.UserRole)
        item_name = self.items_table.item(current_row, 0).text()

        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل تريد حذف الصنف '{item_name}'؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            session = self.db_manager.get_session()
            try:
                item = session.query(Item).get(item_id)
                if item:
                    item.is_active = False  # حذف منطقي
                    session.commit()

                    QMessageBox.information(self, "تم الحذف", f"تم حذف الصنف '{item_name}' بنجاح")
                    self.load_data()
                    self.clear_form()

            except Exception as e:
                session.rollback()
                QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف الصنف:\n{str(e)}")
            finally:
                session.close()
