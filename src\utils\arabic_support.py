# -*- coding: utf-8 -*-
"""
دعم اللغة العربية وتخطيط RTL
Arabic Language and RTL Layout Support
"""

import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt, QLocale
from PySide6.QtGui import QFont, QFontDatabase
import arabic_reshaper
from bidi.algorithm import get_display

def setup_arabic_support(app: QApplication):
    """إعداد دعم اللغة العربية في التطبيق"""
    
    # تعيين اتجاه التخطيط إلى RTL
    app.setLayoutDirection(Qt.RightToLeft)
    
    # إعداد اللغة العربية
    locale = QLocale(QLocale.Arabic, QLocale.SaudiArabia)
    QLocale.setDefault(locale)
    
    # إعداد الخط العربي
    setup_arabic_font(app)

def setup_arabic_font(app: QApplication):
    """إعد<PERSON> الخط العربي للتطبيق"""
    
    # قائمة الخطوط العربية المفضلة
    arabic_fonts = [
        "Segoe UI",
        "Tahoma", 
        "Arial Unicode MS",
        "Times New Roman",
        "Calibri"
    ]
    
    # البحث عن خط عربي متاح
    font_db = QFontDatabase()
    available_fonts = font_db.families()
    
    selected_font = "Tahoma"  # الخط الافتراضي
    
    for font_name in arabic_fonts:
        if font_name in available_fonts:
            selected_font = font_name
            break
    
    # إعداد الخط الافتراضي
    font = QFont(selected_font, 10)
    font.setStyleHint(QFont.SansSerif)
    app.setFont(font)

def reshape_arabic_text(text: str) -> str:
    """تشكيل النص العربي للعرض الصحيح"""
    if not text:
        return text
    
    try:
        # إعادة تشكيل النص العربي
        reshaped_text = arabic_reshaper.reshape(text)
        # تطبيق خوارزمية BiDi
        display_text = get_display(reshaped_text)
        return display_text
    except Exception:
        return text

def format_arabic_number(number) -> str:
    """تنسيق الأرقام للعرض العربي"""
    if number is None:
        return ""
    
    # تحويل الرقم إلى نص مع فواصل الآلاف
    formatted = f"{number:,.2f}" if isinstance(number, float) else f"{number:,}"
    
    return formatted
