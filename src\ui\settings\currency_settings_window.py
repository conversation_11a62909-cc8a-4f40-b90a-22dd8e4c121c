# -*- coding: utf-8 -*-
"""
نافذة إعدادات العملات
Currency Settings Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QFrame, QMessageBox,
                               QMenuBar, QMenu, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QIcon, QAction

from .currency_settings import CurrencySettingsWidget

class CurrencySettingsWindow(QMainWindow):
    """نافذة إعدادات العملات"""
    
    settings_saved = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إعدادات العملات - نظام إدارة الشحنات")
        self.setMinimumSize(900, 700)
        self.resize(1100, 800)
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        title_label = QLabel("إعدادات العملات وأسعار الصرف")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # ويدجت إعدادات العملات
        self.currency_settings_widget = CurrencySettingsWidget()
        main_layout.addWidget(self.currency_settings_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        save_button = QPushButton("حفظ الإعدادات")
        save_button.clicked.connect(self.save_settings)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.close)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        apply_button = QPushButton("تطبيق")
        apply_button.clicked.connect(self.apply_settings)
        apply_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(apply_button)
        buttons_layout.addWidget(cancel_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_settings)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة تحرير
        edit_menu = menubar.addMenu("تحرير")
        
        reset_action = QAction("إعادة تعيين", self)
        reset_action.triggered.connect(self.reset_settings)
        edit_menu.addAction(reset_action)
        
        # قائمة العملات
        currency_menu = menubar.addMenu("العملات")
        
        add_currency_action = QAction("إضافة عملة", self)
        add_currency_action.triggered.connect(self.add_currency)
        currency_menu.addAction(add_currency_action)
        
        update_rates_action = QAction("تحديث أسعار الصرف", self)
        update_rates_action.triggered.connect(self.update_exchange_rates)
        currency_menu.addAction(update_rates_action)
        
        currency_menu.addSeparator()
        
        set_default_action = QAction("تعيين العملة الافتراضية", self)
        set_default_action.triggered.connect(self.set_default_currency)
        currency_menu.addAction(set_default_action)
        
        # قائمة مساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # زر الحفظ
        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_settings)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # زر التطبيق
        apply_action = QAction("تطبيق", self)
        apply_action.triggered.connect(self.apply_settings)
        toolbar.addAction(apply_action)
        
        # زر الإعادة
        reset_action = QAction("إعادة تعيين", self)
        reset_action.triggered.connect(self.reset_settings)
        toolbar.addAction(reset_action)
        
        toolbar.addSeparator()
        
        # زر إضافة عملة
        add_currency_action = QAction("إضافة عملة", self)
        add_currency_action.triggered.connect(self.add_currency)
        toolbar.addAction(add_currency_action)
        
        # زر تحديث الأسعار
        update_rates_action = QAction("تحديث الأسعار", self)
        update_rates_action.triggered.connect(self.update_exchange_rates)
        toolbar.addAction(update_rates_action)
        
        toolbar.addSeparator()
        
        # زر الإغلاق
        close_action = QAction("إغلاق", self)
        close_action.triggered.connect(self.close)
        toolbar.addAction(close_action)
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            if hasattr(self.currency_settings_widget, 'save_settings'):
                self.currency_settings_widget.save_settings()
            
            QMessageBox.information(
                self,
                "تم الحفظ",
                "تم حفظ إعدادات العملات بنجاح"
            )
            
            self.settings_saved.emit()
            self.close()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في الحفظ",
                f"حدث خطأ أثناء حفظ إعدادات العملات:\n{str(e)}"
            )
    
    def apply_settings(self):
        """تطبيق الإعدادات دون إغلاق النافذة"""
        try:
            if hasattr(self.currency_settings_widget, 'save_settings'):
                self.currency_settings_widget.save_settings()
            
            QMessageBox.information(
                self,
                "تم التطبيق",
                "تم تطبيق إعدادات العملات بنجاح"
            )
            
            self.settings_saved.emit()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في التطبيق",
                f"حدث خطأ أثناء تطبيق إعدادات العملات:\n{str(e)}"
            )
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self,
            "إعادة تعيين الإعدادات",
            "هل تريد إعادة تعيين جميع إعدادات العملات إلى القيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                if hasattr(self.currency_settings_widget, 'reset_settings'):
                    self.currency_settings_widget.reset_settings()
                
                QMessageBox.information(
                    self,
                    "تم إعادة التعيين",
                    "تم إعادة تعيين إعدادات العملات إلى القيم الافتراضية"
                )
                
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ في إعادة التعيين",
                    f"حدث خطأ أثناء إعادة تعيين إعدادات العملات:\n{str(e)}"
                )
    
    def add_currency(self):
        """إضافة عملة جديدة"""
        try:
            if hasattr(self.currency_settings_widget, 'add_currency'):
                self.currency_settings_widget.add_currency()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في الإضافة",
                f"حدث خطأ أثناء إضافة العملة:\n{str(e)}"
            )
    
    def update_exchange_rates(self):
        """تحديث أسعار الصرف"""
        try:
            if hasattr(self.currency_settings_widget, 'update_exchange_rates'):
                self.currency_settings_widget.update_exchange_rates()
            
            QMessageBox.information(
                self,
                "تم التحديث",
                "تم تحديث أسعار الصرف بنجاح"
            )
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في التحديث",
                f"حدث خطأ أثناء تحديث أسعار الصرف:\n{str(e)}"
            )
    
    def set_default_currency(self):
        """تعيين العملة الافتراضية"""
        try:
            if hasattr(self.currency_settings_widget, 'set_default_currency'):
                self.currency_settings_widget.set_default_currency()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في التعيين",
                f"حدث خطأ أثناء تعيين العملة الافتراضية:\n{str(e)}"
            )
    
    def show_about(self):
        """عرض معلومات حول النافذة"""
        QMessageBox.about(
            self,
            "حول إعدادات العملات",
            "نافذة إعدادات العملات وأسعار الصرف\n"
            "نظام إدارة الشحنات المتقدم\n"
            "الإصدار 1.0"
        )
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        event.accept()
    
    def refresh_data(self):
        """تحديث البيانات"""
        if hasattr(self.currency_settings_widget, 'load_settings'):
            self.currency_settings_widget.load_settings()
