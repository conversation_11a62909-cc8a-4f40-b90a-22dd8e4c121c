# -*- coding: utf-8 -*-
"""
نافذة إعدادات الصلاحيات
Permissions Settings Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QMessageBox, QMenuBar, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QAction

from .permissions_settings import PermissionsSettingsWidget

class PermissionsSettingsWindow(QMainWindow):
    """نافذة إعدادات الصلاحيات"""
    
    settings_saved = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إعدادات الصلاحيات - نظام إدارة الشحنات")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        title_label = QLabel("إدارة الصلاحيات والأذونات")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # ويدجت إعدادات الصلاحيات
        self.permissions_settings_widget = PermissionsSettingsWidget()
        main_layout.addWidget(self.permissions_settings_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        save_button = QPushButton("حفظ الإعدادات")
        save_button.clicked.connect(self.save_settings)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        apply_button = QPushButton("تطبيق")
        apply_button.clicked.connect(self.apply_settings)
        apply_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.close)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(apply_button)
        buttons_layout.addWidget(cancel_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_settings)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة الصلاحيات
        permissions_menu = menubar.addMenu("الصلاحيات")
        
        create_role_action = QAction("إنشاء دور جديد", self)
        create_role_action.triggered.connect(self.create_role)
        permissions_menu.addAction(create_role_action)
        
        edit_role_action = QAction("تعديل دور", self)
        edit_role_action.triggered.connect(self.edit_role)
        permissions_menu.addAction(edit_role_action)
        
        delete_role_action = QAction("حذف دور", self)
        delete_role_action.triggered.connect(self.delete_role)
        permissions_menu.addAction(delete_role_action)
        
        permissions_menu.addSeparator()
        
        assign_permissions_action = QAction("تعيين صلاحيات", self)
        assign_permissions_action.triggered.connect(self.assign_permissions)
        permissions_menu.addAction(assign_permissions_action)
        
        # قائمة الأمان
        security_menu = menubar.addMenu("الأمان")
        
        backup_permissions_action = QAction("نسخ احتياطي للصلاحيات", self)
        backup_permissions_action.triggered.connect(self.backup_permissions)
        security_menu.addAction(backup_permissions_action)
        
        restore_permissions_action = QAction("استعادة الصلاحيات", self)
        restore_permissions_action.triggered.connect(self.restore_permissions)
        security_menu.addAction(restore_permissions_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        save_action = QAction("حفظ", self)
        save_action.triggered.connect(self.save_settings)
        toolbar.addAction(save_action)
        
        apply_action = QAction("تطبيق", self)
        apply_action.triggered.connect(self.apply_settings)
        toolbar.addAction(apply_action)
        
        toolbar.addSeparator()
        
        create_role_action = QAction("إنشاء دور", self)
        create_role_action.triggered.connect(self.create_role)
        toolbar.addAction(create_role_action)
        
        edit_role_action = QAction("تعديل دور", self)
        edit_role_action.triggered.connect(self.edit_role)
        toolbar.addAction(edit_role_action)
        
        assign_permissions_action = QAction("تعيين صلاحيات", self)
        assign_permissions_action.triggered.connect(self.assign_permissions)
        toolbar.addAction(assign_permissions_action)
        
        toolbar.addSeparator()
        
        close_action = QAction("إغلاق", self)
        close_action.triggered.connect(self.close)
        toolbar.addAction(close_action)
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            if hasattr(self.permissions_settings_widget, 'save_settings'):
                self.permissions_settings_widget.save_settings()
            
            QMessageBox.information(self, "تم الحفظ", "تم حفظ إعدادات الصلاحيات بنجاح")
            self.settings_saved.emit()
            self.close()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"حدث خطأ أثناء حفظ إعدادات الصلاحيات:\n{str(e)}")
    
    def apply_settings(self):
        """تطبيق الإعدادات"""
        try:
            if hasattr(self.permissions_settings_widget, 'save_settings'):
                self.permissions_settings_widget.save_settings()
            
            QMessageBox.information(self, "تم التطبيق", "تم تطبيق إعدادات الصلاحيات بنجاح")
            self.settings_saved.emit()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التطبيق", f"حدث خطأ أثناء تطبيق إعدادات الصلاحيات:\n{str(e)}")
    
    def create_role(self):
        """إنشاء دور جديد"""
        try:
            if hasattr(self.permissions_settings_widget, 'create_role'):
                self.permissions_settings_widget.create_role()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الإنشاء", f"حدث خطأ أثناء إنشاء الدور:\n{str(e)}")
    
    def edit_role(self):
        """تعديل دور"""
        try:
            if hasattr(self.permissions_settings_widget, 'edit_role'):
                self.permissions_settings_widget.edit_role()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التعديل", f"حدث خطأ أثناء تعديل الدور:\n{str(e)}")
    
    def delete_role(self):
        """حذف دور"""
        try:
            if hasattr(self.permissions_settings_widget, 'delete_role'):
                self.permissions_settings_widget.delete_role()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف الدور:\n{str(e)}")
    
    def assign_permissions(self):
        """تعيين صلاحيات"""
        try:
            if hasattr(self.permissions_settings_widget, 'assign_permissions'):
                self.permissions_settings_widget.assign_permissions()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التعيين", f"حدث خطأ أثناء تعيين الصلاحيات:\n{str(e)}")
    
    def backup_permissions(self):
        """نسخ احتياطي للصلاحيات"""
        try:
            if hasattr(self.permissions_settings_widget, 'backup_permissions'):
                self.permissions_settings_widget.backup_permissions()
            QMessageBox.information(self, "تم النسخ", "تم إنشاء نسخة احتياطية من الصلاحيات بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ في النسخ", f"حدث خطأ أثناء إنشاء النسخة الاحتياطية:\n{str(e)}")
    
    def restore_permissions(self):
        """استعادة الصلاحيات"""
        reply = QMessageBox.question(
            self, "استعادة الصلاحيات",
            "هل تريد استعادة الصلاحيات من النسخة الاحتياطية؟\nسيتم استبدال الصلاحيات الحالية.",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                if hasattr(self.permissions_settings_widget, 'restore_permissions'):
                    self.permissions_settings_widget.restore_permissions()
                QMessageBox.information(self, "تم الاستعادة", "تم استعادة الصلاحيات من النسخة الاحتياطية بنجاح")
            except Exception as e:
                QMessageBox.critical(self, "خطأ في الاستعادة", f"حدث خطأ أثناء استعادة الصلاحيات:\n{str(e)}")
    
    def refresh_data(self):
        """تحديث البيانات"""
        if hasattr(self.permissions_settings_widget, 'load_settings'):
            self.permissions_settings_widget.load_settings()
