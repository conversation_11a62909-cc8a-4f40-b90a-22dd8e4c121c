# -*- coding: utf-8 -*-
"""
نافذة إدارة الأصناف الرئيسية
Main Items Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTabWidget, QLabel, QPushButton, QFrame, QGridLayout,
                               QMessageBox, QMenuBar, QMenu, QAction, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from .units_management import UnitsManagementWidget
from .item_groups_management import ItemGroupsManagementWidget
from .items_management import ItemsManagementWidget

class ItemsWindow(QMainWindow):
    """نافذة إدارة الأصناف الرئيسية"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة الأصناف - نظام إدارة الشحنات")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        title_label = QLabel("إدارة الأصناف والمخزون")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # تبويبات إدارة الأصناف
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        self.tab_widget.setStyleSheet("""
            QTabWidget::pane {
                border: 2px solid #bdc3c7;
                border-radius: 5px;
                background-color: white;
            }
            QTabBar::tab {
                background-color: #ecf0f1;
                padding: 10px 20px;
                margin-right: 2px;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
                font-weight: bold;
            }
            QTabBar::tab:selected {
                background-color: #3498db;
                color: white;
            }
            QTabBar::tab:hover {
                background-color: #5dade2;
                color: white;
            }
        """)
        
        # إضافة التبويبات
        self.setup_tabs()
        
        main_layout.addWidget(self.tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        refresh_button = QPushButton("تحديث البيانات")
        refresh_button.clicked.connect(self.refresh_all_data)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        export_button = QPushButton("تصدير البيانات")
        export_button.clicked.connect(self.export_data)
        export_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.close)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 6px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addWidget(export_button)
        buttons_layout.addWidget(close_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_tabs(self):
        """إعداد تبويبات إدارة الأصناف"""
        
        # تبويب وحدات القياس
        self.units_widget = UnitsManagementWidget()
        self.tab_widget.addTab(self.units_widget, "وحدات القياس")
        
        # تبويب مجموعات الأصناف
        self.groups_widget = ItemGroupsManagementWidget()
        self.tab_widget.addTab(self.groups_widget, "مجموعات الأصناف")
        
        # تبويب الأصناف
        self.items_widget = ItemsManagementWidget()
        self.tab_widget.addTab(self.items_widget, "الأصناف")
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        new_item_action = QAction("صنف جديد", self)
        new_item_action.setShortcut("Ctrl+N")
        new_item_action.triggered.connect(self.new_item)
        file_menu.addAction(new_item_action)
        
        file_menu.addSeparator()
        
        export_action = QAction("تصدير البيانات", self)
        export_action.setShortcut("Ctrl+E")
        export_action.triggered.connect(self.export_data)
        file_menu.addAction(export_action)
        
        import_action = QAction("استيراد البيانات", self)
        import_action.setShortcut("Ctrl+I")
        import_action.triggered.connect(self.import_data)
        file_menu.addAction(import_action)
        
        file_menu.addSeparator()
        
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة البيانات
        data_menu = menubar.addMenu("البيانات")
        
        refresh_action = QAction("تحديث البيانات", self)
        refresh_action.setShortcut("F5")
        refresh_action.triggered.connect(self.refresh_all_data)
        data_menu.addAction(refresh_action)
        
        data_menu.addSeparator()
        
        units_action = QAction("إدارة وحدات القياس", self)
        units_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(0))
        data_menu.addAction(units_action)
        
        groups_action = QAction("إدارة مجموعات الأصناف", self)
        groups_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(1))
        data_menu.addAction(groups_action)
        
        items_action = QAction("إدارة الأصناف", self)
        items_action.triggered.connect(lambda: self.tab_widget.setCurrentIndex(2))
        data_menu.addAction(items_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu("التقارير")
        
        items_report_action = QAction("تقرير الأصناف", self)
        items_report_action.triggered.connect(self.generate_items_report)
        reports_menu.addAction(items_report_action)
        
        groups_report_action = QAction("تقرير مجموعات الأصناف", self)
        groups_report_action.triggered.connect(self.generate_groups_report)
        reports_menu.addAction(groups_report_action)
        
        stock_report_action = QAction("تقرير المخزون", self)
        stock_report_action.triggered.connect(self.generate_stock_report)
        reports_menu.addAction(stock_report_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("أدوات الأصناف")
        
        new_action = QAction("جديد", self)
        new_action.triggered.connect(self.new_item)
        toolbar.addAction(new_action)
        
        toolbar.addSeparator()
        
        refresh_action = QAction("تحديث", self)
        refresh_action.triggered.connect(self.refresh_all_data)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        export_action = QAction("تصدير", self)
        export_action.triggered.connect(self.export_data)
        toolbar.addAction(export_action)
    
    def refresh_all_data(self):
        """تحديث جميع البيانات"""
        try:
            # تحديث بيانات كل تبويب
            self.units_widget.load_data()
            self.groups_widget.load_data()
            self.items_widget.load_data()
            
            QMessageBox.information(
                self,
                "تم التحديث",
                "تم تحديث جميع البيانات بنجاح"
            )
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في التحديث",
                f"حدث خطأ أثناء تحديث البيانات:\n{str(e)}"
            )
    
    def new_item(self):
        """إضافة صنف جديد"""
        # التبديل إلى تبويب الأصناف
        self.tab_widget.setCurrentIndex(2)
        # تفعيل وضع الإضافة في تبويب الأصناف
        self.items_widget.add_new_item()
    
    def export_data(self):
        """تصدير البيانات"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة تصدير البيانات قيد التطوير"
        )
    
    def import_data(self):
        """استيراد البيانات"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة استيراد البيانات قيد التطوير"
        )
    
    def generate_items_report(self):
        """إنتاج تقرير الأصناف"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة تقرير الأصناف قيد التطوير"
        )
    
    def generate_groups_report(self):
        """إنتاج تقرير مجموعات الأصناف"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة تقرير مجموعات الأصناف قيد التطوير"
        )
    
    def generate_stock_report(self):
        """إنتاج تقرير المخزون"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة تقرير المخزون قيد التطوير"
        )
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        # يمكن إضافة تحقق من التغييرات غير المحفوظة هنا
        event.accept()
