# -*- coding: utf-8 -*-
"""
نافذة قائمة إدارة الأصناف
Items Management Menu Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QFrame, QGridLayout,
                               QMessageBox, QMenuBar, QMenu, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QIcon, QAction

from .units_management_window import UnitsManagementWindow
from .item_groups_management_window import ItemGroupsManagementWindow
from .items_management_window import ItemsManagementWindow

class ItemsWindow(QMainWindow):
    """نافذة قائمة إدارة الأصناف"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("قائمة إدارة الأصناف - نظام إدارة الشحنات")
        self.setMinimumSize(800, 600)
        self.resize(900, 650)

        # النوافذ المنفصلة
        self.units_management_window = None
        self.groups_management_window = None
        self.items_management_window = None

        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""

        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel("قائمة إدارة الأصناف")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        main_layout.addWidget(title_label)

        # شبكة أزرار إدارة الأصناف
        items_grid = QGridLayout()
        items_grid.setSpacing(20)

        # إنشاء أزرار إدارة الأصناف
        self.create_items_buttons(items_grid)

        main_layout.addLayout(items_grid)

        # مساحة فارغة
        main_layout.addStretch()
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.close)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 30px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        buttons_layout.addWidget(close_button)
        main_layout.addLayout(buttons_layout)

    def create_items_buttons(self, grid_layout):
        """إنشاء أزرار إدارة الأصناف"""

        # زر إدارة وحدات القياس
        units_button = QPushButton("وحدات القياس")
        units_button.clicked.connect(self.open_units_management)
        units_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-height: 80px;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: scale(1.05);
            }
        """)
        grid_layout.addWidget(units_button, 0, 0)

        # زر إدارة مجموعات الأصناف
        groups_button = QPushButton("مجموعات الأصناف")
        groups_button.clicked.connect(self.open_groups_management)
        groups_button.setStyleSheet("""
            QPushButton {
                background-color: #e67e22;
                color: white;
                border: none;
                padding: 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-height: 80px;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #d35400;
                transform: scale(1.05);
            }
        """)
        grid_layout.addWidget(groups_button, 0, 1)

        # زر إدارة الأصناف
        items_button = QPushButton("إدارة الأصناف")
        items_button.clicked.connect(self.open_items_management)
        items_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 30px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                min-height: 80px;
                min-width: 200px;
            }
            QPushButton:hover {
                background-color: #229954;
                transform: scale(1.05);
            }
        """)
        grid_layout.addWidget(items_button, 1, 0, 1, 2)

    def open_units_management(self):
        """فتح نافذة إدارة وحدات القياس"""
        if self.units_management_window is None or not self.units_management_window.isVisible():
            self.units_management_window = UnitsManagementWindow(self)
            self.units_management_window.data_saved.connect(self.on_data_saved)
        self.units_management_window.show()
        self.units_management_window.raise_()
        self.units_management_window.activateWindow()

    def open_groups_management(self):
        """فتح نافذة إدارة مجموعات الأصناف"""
        if self.groups_management_window is None or not self.groups_management_window.isVisible():
            self.groups_management_window = ItemGroupsManagementWindow(self)
            self.groups_management_window.data_saved.connect(self.on_data_saved)
        self.groups_management_window.show()
        self.groups_management_window.raise_()
        self.groups_management_window.activateWindow()

    def open_items_management(self):
        """فتح نافذة إدارة الأصناف"""
        try:
            if self.items_management_window is None or not self.items_management_window.isVisible():
                self.items_management_window = ItemsManagementWindow(self)
                self.items_management_window.data_saved.connect(self.on_data_saved)
            self.items_management_window.show()
            self.items_management_window.raise_()
            self.items_management_window.activateWindow()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء فتح نافذة إدارة الأصناف:\n{str(e)}")

    def on_data_saved(self):
        """معالج حفظ البيانات"""
        QMessageBox.information(self, "تم الحفظ", "تم حفظ البيانات بنجاح")

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")

        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+Q")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)

        # قائمة الأصناف
        items_menu = menubar.addMenu("الأصناف")

        units_action = QAction("وحدات القياس", self)
        units_action.triggered.connect(self.open_units_management)
        items_menu.addAction(units_action)

        groups_action = QAction("مجموعات الأصناف", self)
        groups_action.triggered.connect(self.open_groups_management)
        items_menu.addAction(groups_action)

        items_action = QAction("إدارة الأصناف", self)
        items_action.triggered.connect(self.open_items_management)
        items_menu.addAction(items_action)

    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # وحدات القياس
        units_action = QAction("وحدات القياس", self)
        units_action.triggered.connect(self.open_units_management)
        toolbar.addAction(units_action)

        # مجموعات الأصناف
        groups_action = QAction("مجموعات الأصناف", self)
        groups_action.triggered.connect(self.open_groups_management)
        toolbar.addAction(groups_action)

        # إدارة الأصناف
        items_action = QAction("إدارة الأصناف", self)
        items_action.triggered.connect(self.open_items_management)
        toolbar.addAction(items_action)

        toolbar.addSeparator()

        # إغلاق
        close_action = QAction("إغلاق", self)
        close_action.triggered.connect(self.close)
        toolbar.addAction(close_action)


    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        # إغلاق النوافذ المفتوحة
        if self.units_management_window and self.units_management_window.isVisible():
            self.units_management_window.close()
        if self.groups_management_window and self.groups_management_window.isVisible():
            self.groups_management_window.close()
        if self.items_management_window and self.items_management_window.isVisible():
            self.items_management_window.close()

        event.accept()
