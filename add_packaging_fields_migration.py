#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Migration script to add packaging and weight fields to items table
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from database.database_manager import DatabaseManager
from sqlalchemy import text

def run_migration():
    """تشغيل migration لإضافة الحقول الجديدة"""
    db_manager = DatabaseManager()
    
    try:
        # الحصول على اتصال مباشر بقاعدة البيانات
        engine = db_manager.engine
        
        with engine.connect() as connection:
            # التحقق من وجود الحقول أولاً
            result = connection.execute(text("PRAGMA table_info(items)"))
            columns = [row[1] for row in result.fetchall()]
            
            # إضافة الحقول الجديدة إذا لم تكن موجودة
            new_columns = [
                ('total_package', 'INTEGER DEFAULT 0'),
                ('partial_package', 'INTEGER DEFAULT 0'),
                ('gram_weight', 'REAL DEFAULT 0.0'),
                ('item_weight', 'REAL DEFAULT 0.0')
            ]
            
            for column_name, column_def in new_columns:
                if column_name not in columns:
                    try:
                        sql = f"ALTER TABLE items ADD COLUMN {column_name} {column_def}"
                        connection.execute(text(sql))
                        print(f"✅ تم إضافة العمود: {column_name}")
                    except Exception as e:
                        print(f"❌ خطأ في إضافة العمود {column_name}: {str(e)}")
                else:
                    print(f"⚠️  العمود {column_name} موجود بالفعل")
            
            # تأكيد التغييرات
            connection.commit()
            print("\n🎉 تم تطبيق Migration بنجاح!")
            
    except Exception as e:
        print(f"❌ خطأ في تطبيق Migration: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    print("🚀 بدء تطبيق Migration لإضافة حقول العبوات والأوزان...")
    success = run_migration()
    
    if success:
        print("\n✅ تم الانتهاء من Migration بنجاح!")
        print("يمكنك الآن استخدام الحقول الجديدة:")
        print("- عبوة الكلي")
        print("- عبوة الجزئي") 
        print("- الجرام")
        print("- وزن الصنف")
    else:
        print("\n❌ فشل في تطبيق Migration!")
        sys.exit(1)
