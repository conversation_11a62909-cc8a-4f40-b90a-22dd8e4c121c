# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات
Database Models
"""

from sqlalchemy import Column, Integer, String, Float, DateTime, Boolean, Text, ForeignKey
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship
from datetime import datetime

Base = declarative_base()

class Company(Base):
    """جدول بيانات الشركة"""
    __tablename__ = 'companies'
    
    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False, comment='اسم الشركة')
    name_en = Column(String(200), comment='اسم الشركة بالإنجليزية')
    address = Column(Text, comment='العنوان')
    phone = Column(String(50), comment='الهاتف')
    email = Column(String(100), comment='البريد الإلكتروني')
    tax_number = Column(String(50), comment='الرقم الضريبي')
    commercial_register = Column(String(50), comment='السجل التجاري')
    logo_path = Column(String(500), comment='مسار الشعار')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class Branch(Base):
    """جدول الفروع"""
    __tablename__ = 'branches'
    
    id = Column(Integer, primary_key=True)
    company_id = Column(Integer, ForeignKey('companies.id'), nullable=False)
    name = Column(String(200), nullable=False, comment='اسم الفرع')
    address = Column(Text, comment='العنوان')
    phone = Column(String(50), comment='الهاتف')
    manager_name = Column(String(100), comment='اسم المدير')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    
    company = relationship("Company", back_populates="branches")

Company.branches = relationship("Branch", back_populates="company")

class User(Base):
    """جدول المستخدمين"""
    __tablename__ = 'users'
    
    id = Column(Integer, primary_key=True)
    username = Column(String(50), unique=True, nullable=False, comment='اسم المستخدم')
    password_hash = Column(String(255), nullable=False, comment='كلمة المرور المشفرة')
    full_name = Column(String(100), nullable=False, comment='الاسم الكامل')
    email = Column(String(100), comment='البريد الإلكتروني')
    phone = Column(String(50), comment='الهاتف')
    role = Column(String(50), default='user', comment='الدور')
    is_active = Column(Boolean, default=True, comment='نشط')
    last_login = Column(DateTime, comment='آخر تسجيل دخول')
    created_at = Column(DateTime, default=datetime.now)

class Currency(Base):
    """جدول العملات"""
    __tablename__ = 'currencies'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(3), unique=True, nullable=False, comment='رمز العملة')
    name = Column(String(100), nullable=False, comment='اسم العملة')
    name_en = Column(String(100), comment='اسم العملة بالإنجليزية')
    symbol = Column(String(10), comment='رمز العملة')
    exchange_rate = Column(Float, default=1.0, comment='سعر الصرف')
    is_base = Column(Boolean, default=False, comment='العملة الأساسية')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)

class FiscalYear(Base):
    """جدول السنوات المالية"""
    __tablename__ = 'fiscal_years'
    
    id = Column(Integer, primary_key=True)
    year = Column(Integer, nullable=False, comment='السنة')
    start_date = Column(DateTime, nullable=False, comment='تاريخ البداية')
    end_date = Column(DateTime, nullable=False, comment='تاريخ النهاية')
    is_current = Column(Boolean, default=False, comment='السنة الحالية')
    is_closed = Column(Boolean, default=False, comment='مغلقة')
    created_at = Column(DateTime, default=datetime.now)

class UnitOfMeasure(Base):
    """جدول وحدات القياس"""
    __tablename__ = 'units_of_measure'

    id = Column(Integer, primary_key=True)
    name = Column(String(100), nullable=False, comment='اسم الوحدة')
    name_en = Column(String(100), comment='اسم الوحدة بالإنجليزية')
    symbol = Column(String(10), comment='رمز الوحدة')
    symbol_en = Column(String(10), comment='رمز الوحدة بالإنجليزية')
    description = Column(Text, comment='وصف الوحدة')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class ItemGroup(Base):
    """جدول مجموعات الأصناف"""
    __tablename__ = 'item_groups'

    id = Column(Integer, primary_key=True)
    name = Column(String(200), nullable=False, comment='اسم المجموعة')
    name_en = Column(String(200), comment='اسم المجموعة بالإنجليزية')
    code = Column(String(50), unique=True, comment='كود المجموعة')
    description = Column(Text, comment='الوصف')
    parent_id = Column(Integer, ForeignKey('item_groups.id'), comment='المجموعة الأب')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    parent = relationship("ItemGroup", remote_side=[id])
    children = relationship("ItemGroup")

class Item(Base):
    """جدول الأصناف"""
    __tablename__ = 'items'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(50), unique=True, nullable=False, comment='كود الصنف')
    name = Column(String(200), nullable=False, comment='اسم الصنف')
    name_en = Column(String(200), comment='اسم الصنف بالإنجليزية')
    description = Column(Text, comment='الوصف')
    group_id = Column(Integer, ForeignKey('item_groups.id'), comment='مجموعة الصنف')
    unit_id = Column(Integer, ForeignKey('units_of_measure.id'), comment='وحدة القياس')
    cost_price = Column(Float, default=0.0, comment='سعر التكلفة')
    selling_price = Column(Float, default=0.0, comment='سعر البيع')
    weight = Column(Float, comment='الوزن')
    dimensions = Column(String(100), comment='الأبعاد')
    total_package = Column(Integer, default=0, comment='عبوة الكلي')
    partial_package = Column(Integer, default=0, comment='عبوة الجزئي')
    gram_weight = Column(Float, default=0.0, comment='الوزن بالجرام')
    item_weight = Column(Float, default=0.0, comment='وزن الصنف')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    group = relationship("ItemGroup")
    unit = relationship("UnitOfMeasure")

class Supplier(Base):
    """جدول الموردين"""
    __tablename__ = 'suppliers'
    
    id = Column(Integer, primary_key=True)
    code = Column(String(50), unique=True, nullable=False, comment='كود المورد')
    name = Column(String(200), nullable=False, comment='اسم المورد')
    name_en = Column(String(200), comment='اسم المورد بالإنجليزية')
    contact_person = Column(String(100), comment='الشخص المسؤول')
    address = Column(Text, comment='العنوان')
    phone = Column(String(50), comment='الهاتف')
    email = Column(String(100), comment='البريد الإلكتروني')
    tax_number = Column(String(50), comment='الرقم الضريبي')
    payment_terms = Column(String(100), comment='شروط الدفع')
    credit_limit = Column(Float, default=0.0, comment='حد الائتمان')
    is_active = Column(Boolean, default=True, comment='نشط')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

class SystemSettings(Base):
    """جدول إعدادات النظام"""
    __tablename__ = 'system_settings'
    
    id = Column(Integer, primary_key=True)
    key = Column(String(100), unique=True, nullable=False, comment='مفتاح الإعداد')
    value = Column(Text, comment='قيمة الإعداد')
    description = Column(Text, comment='وصف الإعداد')
    category = Column(String(50), comment='فئة الإعداد')
    data_type = Column(String(20), default='string', comment='نوع البيانات')
    is_system = Column(Boolean, default=False, comment='إعداد نظام')
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
