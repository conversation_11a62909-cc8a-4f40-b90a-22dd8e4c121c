# -*- coding: utf-8 -*-
"""
نافذة إدارة مجموعات الأصناف
Item Groups Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QMessageBox, QMenuBar, QAction, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from .item_groups_management import ItemGroupsManagementWidget

class ItemGroupsManagementWindow(QMainWindow):
    """نافذة إدارة مجموعات الأصناف"""
    
    data_saved = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة مجموعات الأصناف - نظام إدارة الشحنات")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        title_label = QLabel("إدارة مجموعات الأصناف")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # ويدجت إدارة مجموعات الأصناف
        self.groups_management_widget = ItemGroupsManagementWidget()
        main_layout.addWidget(self.groups_management_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        save_button = QPushButton("حفظ التغييرات")
        save_button.clicked.connect(self.save_data)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        refresh_button = QPushButton("تحديث البيانات")
        refresh_button.clicked.connect(self.refresh_data)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.close)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addWidget(close_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_data)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة مجموعات الأصناف
        groups_menu = menubar.addMenu("مجموعات الأصناف")
        
        add_group_action = QAction("إضافة مجموعة جديدة", self)
        add_group_action.triggered.connect(self.add_group)
        groups_menu.addAction(add_group_action)
        
        edit_group_action = QAction("تعديل مجموعة", self)
        edit_group_action.triggered.connect(self.edit_group)
        groups_menu.addAction(edit_group_action)
        
        delete_group_action = QAction("حذف مجموعة", self)
        delete_group_action.triggered.connect(self.delete_group)
        groups_menu.addAction(delete_group_action)
        
        groups_menu.addSeparator()
        
        move_group_action = QAction("نقل مجموعة", self)
        move_group_action.triggered.connect(self.move_group)
        groups_menu.addAction(move_group_action)
        
        merge_groups_action = QAction("دمج مجموعات", self)
        merge_groups_action.triggered.connect(self.merge_groups)
        groups_menu.addAction(merge_groups_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu("التقارير")
        
        groups_report_action = QAction("تقرير المجموعات", self)
        groups_report_action.triggered.connect(self.generate_groups_report)
        reports_menu.addAction(groups_report_action)
        
        items_by_group_action = QAction("الأصناف حسب المجموعة", self)
        items_by_group_action.triggered.connect(self.generate_items_by_group_report)
        reports_menu.addAction(items_by_group_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        save_action = QAction("حفظ", self)
        save_action.triggered.connect(self.save_data)
        toolbar.addAction(save_action)
        
        refresh_action = QAction("تحديث", self)
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        add_action = QAction("إضافة مجموعة", self)
        add_action.triggered.connect(self.add_group)
        toolbar.addAction(add_action)
        
        edit_action = QAction("تعديل مجموعة", self)
        edit_action.triggered.connect(self.edit_group)
        toolbar.addAction(edit_action)
        
        delete_action = QAction("حذف مجموعة", self)
        delete_action.triggered.connect(self.delete_group)
        toolbar.addAction(delete_action)
        
        toolbar.addSeparator()
        
        report_action = QAction("تقرير المجموعات", self)
        report_action.triggered.connect(self.generate_groups_report)
        toolbar.addAction(report_action)
        
        close_action = QAction("إغلاق", self)
        close_action.triggered.connect(self.close)
        toolbar.addAction(close_action)
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            if hasattr(self.groups_management_widget, 'save_data'):
                self.groups_management_widget.save_data()
            
            QMessageBox.information(self, "تم الحفظ", "تم حفظ بيانات مجموعات الأصناف بنجاح")
            self.data_saved.emit()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}")
    
    def refresh_data(self):
        """تحديث البيانات"""
        try:
            if hasattr(self.groups_management_widget, 'load_data'):
                self.groups_management_widget.load_data()
            QMessageBox.information(self, "تم التحديث", "تم تحديث بيانات مجموعات الأصناف بنجاح")
        except Exception as e:
            QMessageBox.warning(self, "خطأ في التحديث", f"حدث خطأ أثناء تحديث البيانات:\n{str(e)}")
    
    def add_group(self):
        """إضافة مجموعة جديدة"""
        try:
            if hasattr(self.groups_management_widget, 'add_group'):
                self.groups_management_widget.add_group()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الإضافة", f"حدث خطأ أثناء إضافة المجموعة:\n{str(e)}")
    
    def edit_group(self):
        """تعديل مجموعة"""
        try:
            if hasattr(self.groups_management_widget, 'edit_group'):
                self.groups_management_widget.edit_group()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التعديل", f"حدث خطأ أثناء تعديل المجموعة:\n{str(e)}")
    
    def delete_group(self):
        """حذف مجموعة"""
        try:
            if hasattr(self.groups_management_widget, 'delete_group'):
                self.groups_management_widget.delete_group()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف المجموعة:\n{str(e)}")
    
    def move_group(self):
        """نقل مجموعة"""
        try:
            if hasattr(self.groups_management_widget, 'move_group'):
                self.groups_management_widget.move_group()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في النقل", f"حدث خطأ أثناء نقل المجموعة:\n{str(e)}")
    
    def merge_groups(self):
        """دمج مجموعات"""
        try:
            if hasattr(self.groups_management_widget, 'merge_groups'):
                self.groups_management_widget.merge_groups()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الدمج", f"حدث خطأ أثناء دمج المجموعات:\n{str(e)}")
    
    def generate_groups_report(self):
        """إنشاء تقرير المجموعات"""
        try:
            if hasattr(self.groups_management_widget, 'generate_groups_report'):
                self.groups_management_widget.generate_groups_report()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التقرير", f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}")
    
    def generate_items_by_group_report(self):
        """إنشاء تقرير الأصناف حسب المجموعة"""
        try:
            if hasattr(self.groups_management_widget, 'generate_items_by_group_report'):
                self.groups_management_widget.generate_items_by_group_report()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التقرير", f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}")
