# -*- coding: utf-8 -*-
"""
نافذة الإعدادات العامة
General Settings Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QTabWidget, QLabel, QPushButton, QFrame, QGridLayout,
                               QMessageBox, QMenuBar, QMenu, QAction, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from .general_settings import GeneralSettingsWidget
from .fiscal_year_settings import FiscalYearSettingsWidget
from .currency_settings import CurrencySettingsWidget
from .company_settings import CompanySettingsWidget
from .user_settings import UserSettingsWidget
from .permissions_settings import PermissionsSettingsWidget

class SettingsWindow(QMainWindow):
    """نافذة الإعدادات العامة"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("الإعدادات العامة - نظام إدارة الشحنات")
        self.setMinimumSize(1000, 700)
        self.resize(1200, 800)
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        title_label = QLabel("الإعدادات العامة للنظام")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # تبويبات الإعدادات
        self.tab_widget = QTabWidget()
        self.tab_widget.setTabPosition(QTabWidget.North)
        
        # إضافة التبويبات
        self.setup_tabs()
        
        main_layout.addWidget(self.tab_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        save_button = QPushButton("حفظ الإعدادات")
        save_button.clicked.connect(self.save_all_settings)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.close)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(cancel_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_tabs(self):
        """إعداد تبويبات الإعدادات"""
        
        # تبويب المتغيرات العامة
        self.general_widget = GeneralSettingsWidget()
        self.tab_widget.addTab(self.general_widget, "المتغيرات العامة")
        
        # تبويب السنة المالية
        self.fiscal_year_widget = FiscalYearSettingsWidget()
        self.tab_widget.addTab(self.fiscal_year_widget, "السنة المالية")
        
        # تبويب العملات
        self.currency_widget = CurrencySettingsWidget()
        self.tab_widget.addTab(self.currency_widget, "العملات")
        
        # تبويب بيانات الشركة
        self.company_widget = CompanySettingsWidget()
        self.tab_widget.addTab(self.company_widget, "بيانات الشركة")
        
        # تبويب المستخدمين
        self.user_widget = UserSettingsWidget()
        self.tab_widget.addTab(self.user_widget, "المستخدمين")
        
        # تبويب الصلاحيات
        self.permissions_widget = PermissionsSettingsWidget()
        self.tab_widget.addTab(self.permissions_widget, "الصلاحيات")
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        save_action = QAction("حفظ الإعدادات", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_all_settings)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة الإعدادات
        settings_menu = menubar.addMenu("الإعدادات")
        
        reset_action = QAction("إعادة تعيين الافتراضية", self)
        reset_action.triggered.connect(self.reset_to_defaults)
        settings_menu.addAction(reset_action)
        
        export_action = QAction("تصدير الإعدادات", self)
        export_action.triggered.connect(self.export_settings)
        settings_menu.addAction(export_action)
        
        import_action = QAction("استيراد الإعدادات", self)
        import_action.triggered.connect(self.import_settings)
        settings_menu.addAction(import_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("أدوات الإعدادات")
        
        save_action = QAction("حفظ", self)
        save_action.triggered.connect(self.save_all_settings)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        refresh_action = QAction("تحديث", self)
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)
    
    def save_all_settings(self):
        """حفظ جميع الإعدادات"""
        try:
            # حفظ إعدادات كل تبويب
            self.general_widget.save_settings()
            self.fiscal_year_widget.save_settings()
            self.currency_widget.save_settings()
            self.company_widget.save_settings()
            self.user_widget.save_settings()
            self.permissions_widget.save_settings()
            
            QMessageBox.information(
                self,
                "نجح الحفظ",
                "تم حفظ جميع الإعدادات بنجاح"
            )
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في الحفظ",
                f"حدث خطأ أثناء حفظ الإعدادات:\n{str(e)}"
            )
    
    def refresh_data(self):
        """تحديث البيانات"""
        try:
            # تحديث بيانات كل تبويب
            self.general_widget.load_settings()
            self.fiscal_year_widget.load_data()
            self.currency_widget.load_data()
            self.company_widget.load_data()
            self.user_widget.load_data()
            self.permissions_widget.load_data()
            
            QMessageBox.information(
                self,
                "تم التحديث",
                "تم تحديث البيانات بنجاح"
            )
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في التحديث",
                f"حدث خطأ أثناء تحديث البيانات:\n{str(e)}"
            )
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        reply = QMessageBox.question(
            self,
            "تأكيد إعادة التعيين",
            "هل تريد إعادة تعيين جميع الإعدادات إلى القيم الافتراضية؟\nسيتم فقدان الإعدادات الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                # إعادة تعيين الإعدادات لكل تبويب
                self.general_widget.reset_to_defaults()
                self.fiscal_year_widget.reset_to_defaults()
                self.currency_widget.reset_to_defaults()
                self.company_widget.reset_to_defaults()
                self.user_widget.reset_to_defaults()
                self.permissions_widget.reset_to_defaults()
                
                QMessageBox.information(
                    self,
                    "تم إعادة التعيين",
                    "تم إعادة تعيين جميع الإعدادات إلى القيم الافتراضية"
                )
                
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ في إعادة التعيين",
                    f"حدث خطأ أثناء إعادة تعيين الإعدادات:\n{str(e)}"
                )
    
    def export_settings(self):
        """تصدير الإعدادات"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة تصدير الإعدادات قيد التطوير"
        )
    
    def import_settings(self):
        """استيراد الإعدادات"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة استيراد الإعدادات قيد التطوير"
        )
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        # يمكن إضافة تحقق من التغييرات غير المحفوظة هنا
        event.accept()
