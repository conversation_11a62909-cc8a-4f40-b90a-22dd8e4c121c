# -*- coding: utf-8 -*-
"""
نافذة قائمة الإعدادات
Settings Menu Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QFrame, QGridLayout,
                               QMessageBox, QMenuBar, QMenu, QAction, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QIcon

from .general_settings_window import GeneralSettingsWindow
from .fiscal_year_settings_window import FiscalYearSettingsWindow
from .currency_settings_window import CurrencySettingsWindow
from .company_settings_window import CompanySettingsWindow
from .user_settings_window import UserSettingsWindow
from .permissions_settings_window import PermissionsSettingsWindow

class SettingsWindow(QMainWindow):
    """نافذة قائمة الإعدادات"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("قائمة الإعدادات - نظام إدارة الشحنات")
        self.setMinimumSize(800, 600)
        self.resize(900, 650)

        # النوافذ المنفصلة
        self.general_settings_window = None
        self.fiscal_year_settings_window = None
        self.currency_settings_window = None
        self.company_settings_window = None
        self.user_settings_window = None
        self.permissions_settings_window = None

        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""

        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel("قائمة الإعدادات")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(18)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 15px;
            }
        """)
        main_layout.addWidget(title_label)

        # شبكة أزرار الإعدادات
        settings_grid = QGridLayout()
        settings_grid.setSpacing(15)

        # إنشاء أزرار الإعدادات
        self.create_settings_buttons(settings_grid)

        main_layout.addLayout(settings_grid)

        # مساحة فارغة
        main_layout.addStretch()

        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()

        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.close)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 30px;
                border-radius: 5px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)

        buttons_layout.addWidget(close_button)
        main_layout.addLayout(buttons_layout)
    
    def create_settings_buttons(self, grid_layout):
        """إنشاء أزرار الإعدادات"""

        button_style = """
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 20px;
                border-radius: 10px;
                font-weight: bold;
                font-size: 14px;
                text-align: center;
                min-height: 80px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """

        # الإعدادات العامة
        general_btn = QPushButton("الإعدادات العامة\nالمتغيرات والخصائص الأساسية")
        general_btn.setStyleSheet(button_style)
        general_btn.clicked.connect(self.open_general_settings)
        grid_layout.addWidget(general_btn, 0, 0)

        # السنة المالية
        fiscal_btn = QPushButton("السنة المالية\nإعدادات الفترات المالية")
        fiscal_btn.setStyleSheet(button_style.replace("#3498db", "#e67e22").replace("#2980b9", "#d35400").replace("#21618c", "#ba4a00"))
        fiscal_btn.clicked.connect(self.open_fiscal_year_settings)
        grid_layout.addWidget(fiscal_btn, 0, 1)

        # العملات
        currency_btn = QPushButton("العملات\nإدارة العملات وأسعار الصرف")
        currency_btn.setStyleSheet(button_style.replace("#3498db", "#27ae60").replace("#2980b9", "#229954").replace("#21618c", "#1e8449"))
        currency_btn.clicked.connect(self.open_currency_settings)
        grid_layout.addWidget(currency_btn, 1, 0)

        # بيانات الشركة
        company_btn = QPushButton("بيانات الشركة\nمعلومات الشركة والعنوان")
        company_btn.setStyleSheet(button_style.replace("#3498db", "#8e44ad").replace("#2980b9", "#7d3c98").replace("#21618c", "#6c3483"))
        company_btn.clicked.connect(self.open_company_settings)
        grid_layout.addWidget(company_btn, 1, 1)

        # المستخدمين
        users_btn = QPushButton("المستخدمين\nإدارة حسابات المستخدمين")
        users_btn.setStyleSheet(button_style.replace("#3498db", "#e74c3c").replace("#2980b9", "#c0392b").replace("#21618c", "#a93226"))
        users_btn.clicked.connect(self.open_user_settings)
        grid_layout.addWidget(users_btn, 2, 0)

        # الصلاحيات
        permissions_btn = QPushButton("الصلاحيات\nإدارة الأذونات والأدوار")
        permissions_btn.setStyleSheet(button_style.replace("#3498db", "#34495e").replace("#2980b9", "#2c3e50").replace("#21618c", "#1b2631"))
        permissions_btn.clicked.connect(self.open_permissions_settings)
        grid_layout.addWidget(permissions_btn, 2, 1)
    
    def open_general_settings(self):
        """فتح نافذة الإعدادات العامة"""
        if self.general_settings_window is None or not self.general_settings_window.isVisible():
            self.general_settings_window = GeneralSettingsWindow(self)
            self.general_settings_window.settings_saved.connect(self.on_settings_saved)
        self.general_settings_window.show()
        self.general_settings_window.raise_()
        self.general_settings_window.activateWindow()

    def open_fiscal_year_settings(self):
        """فتح نافذة إعدادات السنة المالية"""
        if self.fiscal_year_settings_window is None or not self.fiscal_year_settings_window.isVisible():
            self.fiscal_year_settings_window = FiscalYearSettingsWindow(self)
            self.fiscal_year_settings_window.settings_saved.connect(self.on_settings_saved)
        self.fiscal_year_settings_window.show()
        self.fiscal_year_settings_window.raise_()
        self.fiscal_year_settings_window.activateWindow()

    def open_currency_settings(self):
        """فتح نافذة إعدادات العملات"""
        if self.currency_settings_window is None or not self.currency_settings_window.isVisible():
            self.currency_settings_window = CurrencySettingsWindow(self)
            self.currency_settings_window.settings_saved.connect(self.on_settings_saved)
        self.currency_settings_window.show()
        self.currency_settings_window.raise_()
        self.currency_settings_window.activateWindow()

    def open_company_settings(self):
        """فتح نافذة إعدادات الشركة"""
        if self.company_settings_window is None or not self.company_settings_window.isVisible():
            self.company_settings_window = CompanySettingsWindow(self)
            self.company_settings_window.settings_saved.connect(self.on_settings_saved)
        self.company_settings_window.show()
        self.company_settings_window.raise_()
        self.company_settings_window.activateWindow()

    def open_user_settings(self):
        """فتح نافذة إعدادات المستخدمين"""
        if self.user_settings_window is None or not self.user_settings_window.isVisible():
            self.user_settings_window = UserSettingsWindow(self)
            self.user_settings_window.settings_saved.connect(self.on_settings_saved)
        self.user_settings_window.show()
        self.user_settings_window.raise_()
        self.user_settings_window.activateWindow()

    def open_permissions_settings(self):
        """فتح نافذة إعدادات الصلاحيات"""
        if self.permissions_settings_window is None or not self.permissions_settings_window.isVisible():
            self.permissions_settings_window = PermissionsSettingsWindow(self)
            self.permissions_settings_window.settings_saved.connect(self.on_settings_saved)
        self.permissions_settings_window.show()
        self.permissions_settings_window.raise_()
        self.permissions_settings_window.activateWindow()

    def on_settings_saved(self):
        """عند حفظ الإعدادات من أي نافذة"""
        # يمكن إضافة منطق إضافي هنا عند الحاجة
        pass

    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()

        # قائمة ملف
        file_menu = menubar.addMenu("ملف")

        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)

        # قائمة الإعدادات
        settings_menu = menubar.addMenu("الإعدادات")

        general_action = QAction("الإعدادات العامة", self)
        general_action.triggered.connect(self.open_general_settings)
        settings_menu.addAction(general_action)

        fiscal_action = QAction("السنة المالية", self)
        fiscal_action.triggered.connect(self.open_fiscal_year_settings)
        settings_menu.addAction(fiscal_action)

        currency_action = QAction("العملات", self)
        currency_action.triggered.connect(self.open_currency_settings)
        settings_menu.addAction(currency_action)

        company_action = QAction("بيانات الشركة", self)
        company_action.triggered.connect(self.open_company_settings)
        settings_menu.addAction(company_action)

        users_action = QAction("المستخدمين", self)
        users_action.triggered.connect(self.open_user_settings)
        settings_menu.addAction(users_action)

        permissions_action = QAction("الصلاحيات", self)
        permissions_action.triggered.connect(self.open_permissions_settings)
        settings_menu.addAction(permissions_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("أدوات الإعدادات")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)

        # أزرار فتح النوافذ
        general_action = QAction("الإعدادات العامة", self)
        general_action.triggered.connect(self.open_general_settings)
        toolbar.addAction(general_action)

        fiscal_action = QAction("السنة المالية", self)
        fiscal_action.triggered.connect(self.open_fiscal_year_settings)
        toolbar.addAction(fiscal_action)

        currency_action = QAction("العملات", self)
        currency_action.triggered.connect(self.open_currency_settings)
        toolbar.addAction(currency_action)

        toolbar.addSeparator()

        company_action = QAction("بيانات الشركة", self)
        company_action.triggered.connect(self.open_company_settings)
        toolbar.addAction(company_action)

        users_action = QAction("المستخدمين", self)
        users_action.triggered.connect(self.open_user_settings)
        toolbar.addAction(users_action)

        permissions_action = QAction("الصلاحيات", self)
        permissions_action.triggered.connect(self.open_permissions_settings)
        toolbar.addAction(permissions_action)
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        # إغلاق جميع النوافذ المنفصلة
        if self.general_settings_window and self.general_settings_window.isVisible():
            self.general_settings_window.close()
        if self.fiscal_year_settings_window and self.fiscal_year_settings_window.isVisible():
            self.fiscal_year_settings_window.close()
        if self.currency_settings_window and self.currency_settings_window.isVisible():
            self.currency_settings_window.close()
        if self.company_settings_window and self.company_settings_window.isVisible():
            self.company_settings_window.close()
        if self.user_settings_window and self.user_settings_window.isVisible():
            self.user_settings_window.close()
        if self.permissions_settings_window and self.permissions_settings_window.isVisible():
            self.permissions_settings_window.close()

        event.accept()
