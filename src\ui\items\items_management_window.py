# -*- coding: utf-8 -*-
"""
نافذة إدارة الأصناف
Items Management Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QMessageBox, QMenuBar, QAction, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont

from .items_management import ItemsManagementWidget

class ItemsManagementWindow(QMainWindow):
    """نافذة إدارة الأصناف"""
    
    data_saved = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إدارة الأصناف - نظام إدارة الشحنات")
        self.setMinimumSize(1200, 800)
        self.resize(1400, 900)
        
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        title_label = QLabel("إدارة الأصناف والمنتجات")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # ويدجت إدارة الأصناف
        self.items_management_widget = ItemsManagementWidget()
        main_layout.addWidget(self.items_management_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        save_button = QPushButton("حفظ التغييرات")
        save_button.clicked.connect(self.save_data)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        refresh_button = QPushButton("تحديث البيانات")
        refresh_button.clicked.connect(self.refresh_data)
        refresh_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.close)
        close_button.setStyleSheet("""
            QPushButton {
                background-color: #95a5a6;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #7f8c8d;
            }
        """)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(refresh_button)
        buttons_layout.addWidget(close_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_data)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        import_action = QAction("استيراد أصناف", self)
        import_action.triggered.connect(self.import_items)
        file_menu.addAction(import_action)
        
        export_action = QAction("تصدير أصناف", self)
        export_action.triggered.connect(self.export_items)
        file_menu.addAction(export_action)
        
        file_menu.addSeparator()
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة الأصناف
        items_menu = menubar.addMenu("الأصناف")
        
        add_item_action = QAction("إضافة صنف جديد", self)
        add_item_action.triggered.connect(self.add_item)
        items_menu.addAction(add_item_action)
        
        edit_item_action = QAction("تعديل صنف", self)
        edit_item_action.triggered.connect(self.edit_item)
        items_menu.addAction(edit_item_action)
        
        delete_item_action = QAction("حذف صنف", self)
        delete_item_action.triggered.connect(self.delete_item)
        items_menu.addAction(delete_item_action)
        
        items_menu.addSeparator()
        
        duplicate_item_action = QAction("نسخ صنف", self)
        duplicate_item_action.triggered.connect(self.duplicate_item)
        items_menu.addAction(duplicate_item_action)
        
        move_item_action = QAction("نقل صنف", self)
        move_item_action.triggered.connect(self.move_item)
        items_menu.addAction(move_item_action)
        
        # قائمة المخزون
        inventory_menu = menubar.addMenu("المخزون")
        
        update_stock_action = QAction("تحديث المخزون", self)
        update_stock_action.triggered.connect(self.update_stock)
        inventory_menu.addAction(update_stock_action)
        
        stock_adjustment_action = QAction("تسوية المخزون", self)
        stock_adjustment_action.triggered.connect(self.stock_adjustment)
        inventory_menu.addAction(stock_adjustment_action)
        
        # قائمة التقارير
        reports_menu = menubar.addMenu("التقارير")
        
        items_report_action = QAction("تقرير الأصناف", self)
        items_report_action.triggered.connect(self.generate_items_report)
        reports_menu.addAction(items_report_action)
        
        stock_report_action = QAction("تقرير المخزون", self)
        stock_report_action.triggered.connect(self.generate_stock_report)
        reports_menu.addAction(stock_report_action)
        
        low_stock_report_action = QAction("تقرير النواقص", self)
        low_stock_report_action.triggered.connect(self.generate_low_stock_report)
        reports_menu.addAction(low_stock_report_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        save_action = QAction("حفظ", self)
        save_action.triggered.connect(self.save_data)
        toolbar.addAction(save_action)
        
        refresh_action = QAction("تحديث", self)
        refresh_action.triggered.connect(self.refresh_data)
        toolbar.addAction(refresh_action)
        
        toolbar.addSeparator()
        
        add_action = QAction("إضافة صنف", self)
        add_action.triggered.connect(self.add_item)
        toolbar.addAction(add_action)
        
        edit_action = QAction("تعديل صنف", self)
        edit_action.triggered.connect(self.edit_item)
        toolbar.addAction(edit_action)
        
        delete_action = QAction("حذف صنف", self)
        delete_action.triggered.connect(self.delete_item)
        toolbar.addAction(delete_action)
        
        toolbar.addSeparator()
        
        stock_action = QAction("تحديث المخزون", self)
        stock_action.triggered.connect(self.update_stock)
        toolbar.addAction(stock_action)
        
        report_action = QAction("تقرير الأصناف", self)
        report_action.triggered.connect(self.generate_items_report)
        toolbar.addAction(report_action)
        
        close_action = QAction("إغلاق", self)
        close_action.triggered.connect(self.close)
        toolbar.addAction(close_action)
    
    def save_data(self):
        """حفظ البيانات"""
        try:
            if hasattr(self.items_management_widget, 'save_data'):
                self.items_management_widget.save_data()
            
            QMessageBox.information(self, "تم الحفظ", "تم حفظ بيانات الأصناف بنجاح")
            self.data_saved.emit()
            
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحفظ", f"حدث خطأ أثناء حفظ البيانات:\n{str(e)}")
    
    def refresh_data(self):
        """تحديث البيانات"""
        try:
            if hasattr(self.items_management_widget, 'load_data'):
                self.items_management_widget.load_data()
            QMessageBox.information(self, "تم التحديث", "تم تحديث بيانات الأصناف بنجاح")
        except Exception as e:
            QMessageBox.warning(self, "خطأ في التحديث", f"حدث خطأ أثناء تحديث البيانات:\n{str(e)}")
    
    def add_item(self):
        """إضافة صنف جديد"""
        try:
            if hasattr(self.items_management_widget, 'add_item'):
                self.items_management_widget.add_item()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الإضافة", f"حدث خطأ أثناء إضافة الصنف:\n{str(e)}")
    
    def edit_item(self):
        """تعديل صنف"""
        try:
            if hasattr(self.items_management_widget, 'edit_item'):
                self.items_management_widget.edit_item()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التعديل", f"حدث خطأ أثناء تعديل الصنف:\n{str(e)}")
    
    def delete_item(self):
        """حذف صنف"""
        try:
            if hasattr(self.items_management_widget, 'delete_item'):
                self.items_management_widget.delete_item()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الحذف", f"حدث خطأ أثناء حذف الصنف:\n{str(e)}")
    
    def duplicate_item(self):
        """نسخ صنف"""
        try:
            if hasattr(self.items_management_widget, 'duplicate_item'):
                self.items_management_widget.duplicate_item()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في النسخ", f"حدث خطأ أثناء نسخ الصنف:\n{str(e)}")
    
    def move_item(self):
        """نقل صنف"""
        try:
            if hasattr(self.items_management_widget, 'move_item'):
                self.items_management_widget.move_item()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في النقل", f"حدث خطأ أثناء نقل الصنف:\n{str(e)}")
    
    def update_stock(self):
        """تحديث المخزون"""
        try:
            if hasattr(self.items_management_widget, 'update_stock'):
                self.items_management_widget.update_stock()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التحديث", f"حدث خطأ أثناء تحديث المخزون:\n{str(e)}")
    
    def stock_adjustment(self):
        """تسوية المخزون"""
        try:
            if hasattr(self.items_management_widget, 'stock_adjustment'):
                self.items_management_widget.stock_adjustment()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التسوية", f"حدث خطأ أثناء تسوية المخزون:\n{str(e)}")
    
    def import_items(self):
        """استيراد أصناف"""
        try:
            if hasattr(self.items_management_widget, 'import_items'):
                self.items_management_widget.import_items()
            QMessageBox.information(self, "تم الاستيراد", "تم استيراد الأصناف بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ في الاستيراد", f"حدث خطأ أثناء استيراد الأصناف:\n{str(e)}")
    
    def export_items(self):
        """تصدير أصناف"""
        try:
            if hasattr(self.items_management_widget, 'export_items'):
                self.items_management_widget.export_items()
            QMessageBox.information(self, "تم التصدير", "تم تصدير الأصناف بنجاح")
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التصدير", f"حدث خطأ أثناء تصدير الأصناف:\n{str(e)}")
    
    def generate_items_report(self):
        """إنشاء تقرير الأصناف"""
        try:
            if hasattr(self.items_management_widget, 'generate_items_report'):
                self.items_management_widget.generate_items_report()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التقرير", f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}")
    
    def generate_stock_report(self):
        """إنشاء تقرير المخزون"""
        try:
            if hasattr(self.items_management_widget, 'generate_stock_report'):
                self.items_management_widget.generate_stock_report()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التقرير", f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}")
    
    def generate_low_stock_report(self):
        """إنشاء تقرير النواقص"""
        try:
            if hasattr(self.items_management_widget, 'generate_low_stock_report'):
                self.items_management_widget.generate_low_stock_report()
        except Exception as e:
            QMessageBox.critical(self, "خطأ في التقرير", f"حدث خطأ أثناء إنشاء التقرير:\n{str(e)}")
