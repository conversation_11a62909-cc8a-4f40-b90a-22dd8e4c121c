# -*- coding: utf-8 -*-
"""
النافذة الرئيسية للبرنامج
Main Application Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, 
                               QMenuBar, QMenu, QAction, QToolBar, QStatusBar,
                               QLabel, QPushButton, QFrame, QGridLayout, QMessageBox)
from PySide6.QtCore import Qt, QSize, Signal
from PySide6.QtGui import QIcon, QPixmap, QFont, QAction

from ..utils.arabic_support import reshape_arabic_text
from .settings.settings_window import SettingsWindow
from .items.items_window import ItemsWindow
from .suppliers.suppliers_window import SuppliersWindow

class MainWindow(QMainWindow):
    """النافذة الرئيسية للتطبيق"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("نظام إدارة الشحنات المتكامل - ProShipment")
        self.setMinimumSize(1200, 800)
        
        # تعيين النافذة لتفتح في وضع ملء الشاشة
        self.showMaximized()
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
        self.setup_status_bar()
        
        # متغيرات النوافذ المنفصلة
        self.settings_window = None
        self.items_window = None
        self.suppliers_window = None
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setSpacing(20)
        main_layout.setContentsMargins(20, 20, 20, 20)
        
        # عنوان الترحيب
        welcome_label = QLabel("مرحباً بك في نظام إدارة الشحنات المتكامل")
        welcome_label.setAlignment(Qt.AlignCenter)
        welcome_font = QFont()
        welcome_font.setPointSize(18)
        welcome_font.setBold(True)
        welcome_label.setFont(welcome_font)
        welcome_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 20px;
                background-color: #ecf0f1;
                border-radius: 10px;
                margin-bottom: 20px;
            }
        """)
        main_layout.addWidget(welcome_label)
        
        # شبكة الأزرار الرئيسية
        buttons_frame = QFrame()
        buttons_layout = QGridLayout(buttons_frame)
        buttons_layout.setSpacing(15)
        
        # إنشاء أزرار الأنظمة الرئيسية
        self.create_system_buttons(buttons_layout)
        
        main_layout.addWidget(buttons_frame)
        main_layout.addStretch()
    
    def create_system_buttons(self, layout):
        """إنشاء أزرار الأنظمة الرئيسية"""
        
        # بيانات الأزرار
        buttons_data = [
            ("الإعدادات العامة", "إدارة إعدادات النظام والشركة والمستخدمين", self.open_settings, 0, 0),
            ("إدارة الأصناف", "إدارة الأصناف ووحدات القياس والمجموعات", self.open_items, 0, 1),
            ("إدارة الموردين", "إدارة بيانات الموردين والعمليات والتقارير", self.open_suppliers, 0, 2),
            ("متابعة الشحنات", "نظام متابعة وتتبع الشحنات (قيد التطوير)", self.show_under_development, 1, 0),
            ("الإدخالات الجمركية", "نظام الإدخالات الجمركية (قيد التطوير)", self.show_under_development, 1, 1),
            ("إدارة التكاليف", "نظام إدارة التكاليف (قيد التطوير)", self.show_under_development, 1, 2),
        ]
        
        for text, tooltip, callback, row, col in buttons_data:
            button = self.create_main_button(text, tooltip, callback)
            layout.addWidget(button, row, col)
    
    def create_main_button(self, text, tooltip, callback):
        """إنشاء زر رئيسي"""
        button = QPushButton(text)
        button.setToolTip(tooltip)
        button.setMinimumSize(250, 100)
        button.clicked.connect(callback)
        
        # تنسيق الزر
        button.setStyleSheet("""
            QPushButton {
                font-size: 14px;
                font-weight: bold;
                color: white;
                background-color: #3498db;
                border: none;
                border-radius: 10px;
                padding: 15px;
            }
            QPushButton:hover {
                background-color: #2980b9;
                transform: translateY(-2px);
            }
            QPushButton:pressed {
                background-color: #21618c;
            }
        """)
        
        return button
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        # إجراءات قائمة ملف
        new_action = QAction("جديد", self)
        new_action.setShortcut("Ctrl+N")
        file_menu.addAction(new_action)
        
        open_action = QAction("فتح", self)
        open_action.setShortcut("Ctrl+O")
        file_menu.addAction(open_action)
        
        file_menu.addSeparator()
        
        backup_action = QAction("نسخة احتياطية", self)
        backup_action.triggered.connect(self.create_backup)
        file_menu.addAction(backup_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction("خروج", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # قائمة الأنظمة
        systems_menu = menubar.addMenu("الأنظمة")
        
        settings_action = QAction("الإعدادات العامة", self)
        settings_action.triggered.connect(self.open_settings)
        systems_menu.addAction(settings_action)
        
        items_action = QAction("إدارة الأصناف", self)
        items_action.triggered.connect(self.open_items)
        systems_menu.addAction(items_action)
        
        suppliers_action = QAction("إدارة الموردين", self)
        suppliers_action.triggered.connect(self.open_suppliers)
        systems_menu.addAction(suppliers_action)
        
        # قائمة المساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول البرنامج", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # أزرار شريط الأدوات
        settings_action = QAction("الإعدادات", self)
        settings_action.triggered.connect(self.open_settings)
        toolbar.addAction(settings_action)
        
        items_action = QAction("الأصناف", self)
        items_action.triggered.connect(self.open_items)
        toolbar.addAction(items_action)
        
        suppliers_action = QAction("الموردين", self)
        suppliers_action.triggered.connect(self.open_suppliers)
        toolbar.addAction(suppliers_action)
        
        toolbar.addSeparator()
        
        backup_action = QAction("نسخة احتياطية", self)
        backup_action.triggered.connect(self.create_backup)
        toolbar.addAction(backup_action)
    
    def setup_status_bar(self):
        """إعداد شريط الحالة"""
        status_bar = self.statusBar()
        
        # معلومات الحالة
        self.status_label = QLabel("جاهز")
        status_bar.addWidget(self.status_label)
        
        # معلومات المستخدم
        user_label = QLabel("المستخدم: مدير النظام")
        status_bar.addPermanentWidget(user_label)
        
        # التاريخ والوقت
        from datetime import datetime
        date_label = QLabel(datetime.now().strftime("%Y/%m/%d - %H:%M"))
        status_bar.addPermanentWidget(date_label)
    
    def open_settings(self):
        """فتح نافذة الإعدادات"""
        if self.settings_window is None or not self.settings_window.isVisible():
            self.settings_window = SettingsWindow(self)
        
        self.settings_window.show()
        self.settings_window.raise_()
        self.settings_window.activateWindow()
    
    def open_items(self):
        """فتح نافذة إدارة الأصناف"""
        if self.items_window is None or not self.items_window.isVisible():
            self.items_window = ItemsWindow(self)
        
        self.items_window.show()
        self.items_window.raise_()
        self.items_window.activateWindow()
    
    def open_suppliers(self):
        """فتح نافذة إدارة الموردين"""
        if self.suppliers_window is None or not self.suppliers_window.isVisible():
            self.suppliers_window = SuppliersWindow(self)
        
        self.suppliers_window.show()
        self.suppliers_window.raise_()
        self.suppliers_window.activateWindow()
    
    def show_under_development(self):
        """عرض رسالة قيد التطوير"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "هذه الميزة قيد التطوير وستكون متاحة في الإصدارات القادمة."
        )
    
    def create_backup(self):
        """إنشاء نسخة احتياطية"""
        from ..database.database_manager import DatabaseManager
        
        db_manager = DatabaseManager()
        if db_manager.backup_database():
            QMessageBox.information(
                self,
                "نسخة احتياطية",
                "تم إنشاء النسخة الاحتياطية بنجاح"
            )
        else:
            QMessageBox.warning(
                self,
                "خطأ",
                "فشل في إنشاء النسخة الاحتياطية"
            )
    
    def show_about(self):
        """عرض معلومات البرنامج"""
        QMessageBox.about(
            self,
            "حول البرنامج",
            """
            <h3>نظام إدارة الشحنات المتكامل</h3>
            <p><b>الإصدار:</b> 1.0.0</p>
            <p><b>الوصف:</b> نظام شامل لإدارة الشحنات والموردين والأصناف</p>
            <p><b>المطور:</b> فريق ProShipment</p>
            <p><b>حقوق النشر:</b> © 2024 جميع الحقوق محفوظة</p>
            """
        )
    
    def closeEvent(self, event):
        """حدث إغلاق النافذة"""
        reply = QMessageBox.question(
            self,
            "تأكيد الخروج",
            "هل تريد إغلاق البرنامج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # إغلاق النوافذ المنفصلة
            if self.settings_window:
                self.settings_window.close()
            if self.items_window:
                self.items_window.close()
            if self.suppliers_window:
                self.suppliers_window.close()
            
            event.accept()
        else:
            event.ignore()
