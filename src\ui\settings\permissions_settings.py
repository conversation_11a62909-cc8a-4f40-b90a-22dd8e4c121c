# -*- coding: utf-8 -*-
"""
تبويب إعدادات الصلاحيات
Permissions Settings Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QComboBox, QPushButton, QTreeWidget, QTreeWidgetItem,
                               QGroupBox, QMessageBox, QCheckBox)
from PySide6.QtCore import Qt

from ...database.database_manager import DatabaseManager
from ...database.models import User

class PermissionsSettingsWidget(QWidget):
    """ويدجت إعدادات الصلاحيات"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.permissions_data = {}
        self.setup_ui()
        self.load_users()
        self.setup_permissions_tree()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # مجموعة اختيار المستخدم
        user_group = QGroupBox("اختيار المستخدم")
        user_layout = QFormLayout(user_group)
        
        self.user_combo = QComboBox()
        self.user_combo.currentTextChanged.connect(self.load_user_permissions)
        user_layout.addRow("المستخدم:", self.user_combo)
        
        main_layout.addWidget(user_group)
        
        # مجموعة الصلاحيات
        permissions_group = QGroupBox("الصلاحيات")
        permissions_layout = QVBoxLayout(permissions_group)
        
        # شجرة الصلاحيات
        self.permissions_tree = QTreeWidget()
        self.permissions_tree.setHeaderLabels(["الوحدة/الصلاحية", "مسموح"])
        self.permissions_tree.setRootIsDecorated(True)
        
        permissions_layout.addWidget(self.permissions_tree)
        
        # أزرار الإدارة
        buttons_layout = QHBoxLayout()
        
        save_button = QPushButton("حفظ الصلاحيات")
        save_button.clicked.connect(self.save_permissions)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        select_all_button = QPushButton("تحديد الكل")
        select_all_button.clicked.connect(self.select_all_permissions)
        
        deselect_all_button = QPushButton("إلغاء تحديد الكل")
        deselect_all_button.clicked.connect(self.deselect_all_permissions)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(select_all_button)
        buttons_layout.addWidget(deselect_all_button)
        buttons_layout.addStretch()
        
        permissions_layout.addLayout(buttons_layout)
        main_layout.addWidget(permissions_group)
        
        main_layout.addStretch()
    
    def setup_permissions_tree(self):
        """إعداد شجرة الصلاحيات"""
        
        # تعريف الصلاحيات حسب الوحدات
        permissions_structure = {
            "الإعدادات العامة": [
                "عرض الإعدادات",
                "تعديل الإعدادات",
                "إدارة المستخدمين",
                "إدارة الصلاحيات"
            ],
            "إدارة الأصناف": [
                "عرض الأصناف",
                "إضافة صنف",
                "تعديل صنف",
                "حذف صنف",
                "إدارة مجموعات الأصناف",
                "إدارة وحدات القياس"
            ],
            "إدارة الموردين": [
                "عرض الموردين",
                "إضافة مورد",
                "تعديل مورد",
                "حذف مورد",
                "عرض تقارير الموردين"
            ],
            "تتبع الشحنات": [
                "عرض الشحنات",
                "إضافة شحنة",
                "تعديل شحنة",
                "حذف شحنة",
                "تتبع الشحنات",
                "عرض تقارير الشحنات"
            ],
            "البيانات الجمركية": [
                "عرض البيانات الجمركية",
                "إضافة بيان جمركي",
                "تعديل بيان جمركي",
                "حذف بيان جمركي",
                "عرض تقارير جمركية"
            ],
            "إدارة التكاليف": [
                "عرض التكاليف",
                "إضافة تكلفة",
                "تعديل تكلفة",
                "حذف تكلفة",
                "عرض تقارير التكاليف"
            ],
            "التقارير": [
                "تقارير الأصناف",
                "تقارير الموردين",
                "تقارير الشحنات",
                "تقارير التكاليف",
                "تقارير مالية",
                "تصدير التقارير"
            ]
        }
        
        self.permissions_tree.clear()
        
        for module, permissions in permissions_structure.items():
            # إنشاء عقدة الوحدة
            module_item = QTreeWidgetItem(self.permissions_tree)
            module_item.setText(0, module)
            module_item.setFlags(module_item.flags() | Qt.ItemIsTristate | Qt.ItemIsUserCheckable)
            module_item.setCheckState(0, Qt.Unchecked)
            
            # إضافة الصلاحيات الفرعية
            for permission in permissions:
                permission_item = QTreeWidgetItem(module_item)
                permission_item.setText(0, permission)
                permission_item.setFlags(permission_item.flags() | Qt.ItemIsUserCheckable)
                permission_item.setCheckState(0, Qt.Unchecked)
                
                # حفظ مفتاح الصلاحية
                permission_key = f"{module}:{permission}"
                permission_item.setData(0, Qt.UserRole, permission_key)
        
        self.permissions_tree.expandAll()
    
    def load_users(self):
        """تحميل قائمة المستخدمين"""
        session = self.db_manager.get_session()
        try:
            users = session.query(User).filter_by(is_active=True).all()
            
            self.user_combo.clear()
            self.user_combo.addItem("-- اختر مستخدم --", None)
            
            for user in users:
                display_text = f"{user.full_name} ({user.username})"
                self.user_combo.addItem(display_text, user.id)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل قائمة المستخدمين:\n{str(e)}"
            )
        finally:
            session.close()
    
    def load_user_permissions(self):
        """تحميل صلاحيات المستخدم المحدد"""
        user_id = self.user_combo.currentData()
        if not user_id:
            self.deselect_all_permissions()
            return
        
        # في التطبيق الحقيقي، ستحمل الصلاحيات من قاعدة البيانات
        # هنا سنستخدم صلاحيات افتراضية حسب دور المستخدم
        session = self.db_manager.get_session()
        try:
            user = session.query(User).get(user_id)
            if user:
                self.set_permissions_by_role(user.role)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل الصلاحيات",
                f"حدث خطأ أثناء تحميل صلاحيات المستخدم:\n{str(e)}"
            )
        finally:
            session.close()
    
    def set_permissions_by_role(self, role):
        """تعيين الصلاحيات حسب الدور"""
        # إعادة تعيين جميع الصلاحيات
        self.deselect_all_permissions()
        
        # تعيين الصلاحيات حسب الدور
        if role == "admin":
            # المدير له جميع الصلاحيات
            self.select_all_permissions()
        elif role == "manager":
            # مدير القسم له معظم الصلاحيات
            self.set_manager_permissions()
        elif role == "operator":
            # المشغل له صلاحيات محدودة
            self.set_operator_permissions()
        else:
            # المستخدم العادي له صلاحيات أساسية
            self.set_user_permissions()
    
    def set_manager_permissions(self):
        """تعيين صلاحيات مدير القسم"""
        # صلاحيات العرض والتعديل لمعظم الوحدات
        manager_permissions = [
            "الإعدادات العامة:عرض الإعدادات",
            "إدارة الأصناف:عرض الأصناف",
            "إدارة الأصناف:إضافة صنف",
            "إدارة الأصناف:تعديل صنف",
            "إدارة الموردين:عرض الموردين",
            "إدارة الموردين:إضافة مورد",
            "إدارة الموردين:تعديل مورد",
            "تتبع الشحنات:عرض الشحنات",
            "تتبع الشحنات:إضافة شحنة",
            "تتبع الشحنات:تعديل شحنة",
            "التقارير:تقارير الأصناف",
            "التقارير:تقارير الموردين",
            "التقارير:تقارير الشحنات"
        ]
        self.check_permissions(manager_permissions)
    
    def set_operator_permissions(self):
        """تعيين صلاحيات المشغل"""
        operator_permissions = [
            "إدارة الأصناف:عرض الأصناف",
            "إدارة الموردين:عرض الموردين",
            "تتبع الشحنات:عرض الشحنات",
            "تتبع الشحنات:إضافة شحنة",
            "البيانات الجمركية:عرض البيانات الجمركية"
        ]
        self.check_permissions(operator_permissions)
    
    def set_user_permissions(self):
        """تعيين صلاحيات المستخدم العادي"""
        user_permissions = [
            "إدارة الأصناف:عرض الأصناف",
            "إدارة الموردين:عرض الموردين",
            "تتبع الشحنات:عرض الشحنات"
        ]
        self.check_permissions(user_permissions)
    
    def check_permissions(self, permissions_list):
        """تحديد صلاحيات محددة"""
        for i in range(self.permissions_tree.topLevelItemCount()):
            module_item = self.permissions_tree.topLevelItem(i)
            for j in range(module_item.childCount()):
                permission_item = module_item.child(j)
                permission_key = permission_item.data(0, Qt.UserRole)
                if permission_key in permissions_list:
                    permission_item.setCheckState(0, Qt.Checked)
    
    def select_all_permissions(self):
        """تحديد جميع الصلاحيات"""
        for i in range(self.permissions_tree.topLevelItemCount()):
            module_item = self.permissions_tree.topLevelItem(i)
            module_item.setCheckState(0, Qt.Checked)
    
    def deselect_all_permissions(self):
        """إلغاء تحديد جميع الصلاحيات"""
        for i in range(self.permissions_tree.topLevelItemCount()):
            module_item = self.permissions_tree.topLevelItem(i)
            module_item.setCheckState(0, Qt.Unchecked)
    
    def save_permissions(self):
        """حفظ الصلاحيات"""
        user_id = self.user_combo.currentData()
        if not user_id:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار مستخدم")
            return
        
        # جمع الصلاحيات المحددة
        selected_permissions = []
        for i in range(self.permissions_tree.topLevelItemCount()):
            module_item = self.permissions_tree.topLevelItem(i)
            for j in range(module_item.childCount()):
                permission_item = module_item.child(j)
                if permission_item.checkState(0) == Qt.Checked:
                    permission_key = permission_item.data(0, Qt.UserRole)
                    selected_permissions.append(permission_key)
        
        # في التطبيق الحقيقي، ستحفظ الصلاحيات في قاعدة البيانات
        # هنا سنعرض رسالة تأكيد فقط
        QMessageBox.information(
            self,
            "تم الحفظ",
            f"تم حفظ {len(selected_permissions)} صلاحية للمستخدم المحدد"
        )
    
    def save_settings(self):
        """حفظ الإعدادات"""
        self.save_permissions()
    
    def reset_to_defaults(self):
        """إعادة تعيين الإعدادات الافتراضية"""
        self.deselect_all_permissions()
        self.user_combo.setCurrentIndex(0)
