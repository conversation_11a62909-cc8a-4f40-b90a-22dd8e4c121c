# -*- coding: utf-8 -*-
"""
تبويب عمليات الموردين
Suppliers Operations Tab
"""

from PySide6.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QFormLayout,
                               QLabel, QLineEdit, QPushButton, QTableWidget, QTableWidgetItem,
                               QGroupBox, QMessageBox, QHeaderView, QTextEdit, QCheckBox,
                               QComboBox, QDateEdit, QSpinBox, QDoubleSpinBox)
from PySide6.QtCore import Qt, QDate

from ...database.database_manager import DatabaseManager
from ...database.models import Supplier

class SuppliersOperationsWidget(QWidget):
    """ويدجت عمليات الموردين"""
    
    def __init__(self):
        super().__init__()
        self.db_manager = DatabaseManager()
        self.current_transaction_id = None
        self.setup_ui()
        self.load_data()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        main_layout = QVBoxLayout(self)
        main_layout.setSpacing(15)
        
        # الجزء العلوي - نموذج العمليات
        form_group = QGroupBox("تسجيل عملية جديدة")
        form_layout = QHBoxLayout(form_group)
        
        # الجانب الأيسر - بيانات العملية
        operation_group = QGroupBox("بيانات العملية")
        operation_layout = QFormLayout(operation_group)
        
        self.supplier_combo = QComboBox()
        self.load_suppliers()
        operation_layout.addRow("المورد:", self.supplier_combo)
        
        self.transaction_type_combo = QComboBox()
        self.transaction_type_combo.addItems([
            "فاتورة شراء", "دفعة", "خصم", "إرجاع", "تسوية"
        ])
        operation_layout.addRow("نوع العملية:", self.transaction_type_combo)
        
        self.transaction_date = QDateEdit()
        self.transaction_date.setDate(QDate.currentDate())
        self.transaction_date.setCalendarPopup(True)
        operation_layout.addRow("تاريخ العملية:", self.transaction_date)
        
        self.reference_number_edit = QLineEdit()
        self.reference_number_edit.setPlaceholderText("رقم المرجع أو الفاتورة")
        operation_layout.addRow("رقم المرجع:", self.reference_number_edit)
        
        form_layout.addWidget(operation_group)
        
        # الجانب الأيمن - المبالغ والتفاصيل
        details_group = QGroupBox("التفاصيل والمبالغ")
        details_layout = QFormLayout(details_group)
        
        self.amount_edit = QDoubleSpinBox()
        self.amount_edit.setMaximum(999999999.99)
        self.amount_edit.setSuffix(" ريال")
        self.amount_edit.setDecimals(2)
        details_layout.addRow("المبلغ:", self.amount_edit)
        
        self.currency_combo = QComboBox()
        self.currency_combo.addItems(["ريال سعودي", "دولار أمريكي", "يورو"])
        details_layout.addRow("العملة:", self.currency_combo)
        
        self.exchange_rate_edit = QDoubleSpinBox()
        self.exchange_rate_edit.setMinimum(0.01)
        self.exchange_rate_edit.setMaximum(999.99)
        self.exchange_rate_edit.setValue(1.00)
        self.exchange_rate_edit.setDecimals(4)
        details_layout.addRow("سعر الصرف:", self.exchange_rate_edit)
        
        self.description_edit = QTextEdit()
        self.description_edit.setMaximumHeight(80)
        self.description_edit.setPlaceholderText("وصف العملية أو ملاحظات")
        details_layout.addRow("الوصف:", self.description_edit)
        
        form_layout.addWidget(details_group)
        
        main_layout.addWidget(form_group)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        
        self.add_button = QPushButton("إضافة العملية")
        self.add_button.clicked.connect(self.add_transaction)
        self.add_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        self.update_button = QPushButton("تحديث العملية")
        self.update_button.clicked.connect(self.update_transaction)
        self.update_button.setEnabled(False)
        self.update_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
            QPushButton:disabled {
                background-color: #bdc3c7;
            }
        """)
        
        self.clear_button = QPushButton("مسح النموذج")
        self.clear_button.clicked.connect(self.clear_form)
        
        buttons_layout.addWidget(self.add_button)
        buttons_layout.addWidget(self.update_button)
        buttons_layout.addWidget(self.clear_button)
        buttons_layout.addStretch()
        
        main_layout.addLayout(buttons_layout)
        
        # جدول العمليات
        table_group = QGroupBox("سجل العمليات")
        table_layout = QVBoxLayout(table_group)
        
        self.transactions_table = QTableWidget()
        self.transactions_table.setColumnCount(7)
        self.transactions_table.setHorizontalHeaderLabels([
            "التاريخ", "المورد", "نوع العملية", "رقم المرجع", "المبلغ", "العملة", "الوصف"
        ])
        
        # تنسيق الجدول
        header = self.transactions_table.horizontalHeader()
        header.setSectionResizeMode(QHeaderView.Stretch)
        self.transactions_table.setAlternatingRowColors(True)
        self.transactions_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.transactions_table.itemSelectionChanged.connect(self.on_selection_changed)
        
        table_layout.addWidget(self.transactions_table)
        
        # أزرار إدارة الجدول
        table_buttons_layout = QHBoxLayout()
        
        refresh_button = QPushButton("تحديث")
        refresh_button.clicked.connect(self.load_data)
        
        edit_button = QPushButton("تعديل المحدد")
        edit_button.clicked.connect(self.edit_selected)
        
        delete_button = QPushButton("حذف المحدد")
        delete_button.clicked.connect(self.delete_selected)
        delete_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        filter_button = QPushButton("تصفية حسب المورد")
        filter_button.clicked.connect(self.filter_by_supplier)
        
        table_buttons_layout.addWidget(refresh_button)
        table_buttons_layout.addWidget(edit_button)
        table_buttons_layout.addWidget(delete_button)
        table_buttons_layout.addWidget(filter_button)
        table_buttons_layout.addStretch()
        
        table_layout.addLayout(table_buttons_layout)
        main_layout.addWidget(table_group)
    
    def load_suppliers(self):
        """تحميل قائمة الموردين"""
        session = self.db_manager.get_session()
        try:
            suppliers = session.query(Supplier).filter_by(is_active=True).all()
            
            self.supplier_combo.clear()
            self.supplier_combo.addItem("-- اختر مورد --", None)
            
            for supplier in suppliers:
                self.supplier_combo.addItem(supplier.name, supplier.id)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل قائمة الموردين:\n{str(e)}"
            )
        finally:
            session.close()
    
    def load_data(self):
        """تحميل بيانات العمليات"""
        session = self.db_manager.get_session()
        try:
            # في التطبيق الحقيقي، ستحمل من جدول المعاملات
            # هنا سنعرض بيانات وهمية للتوضيح
            transactions = []
            
            self.transactions_table.setRowCount(len(transactions))
            
            for row, transaction in enumerate(transactions):
                # التاريخ
                self.transactions_table.setItem(row, 0, QTableWidgetItem(str(transaction.date)))
                
                # المورد
                supplier_name = transaction.supplier.name if transaction.supplier else ""
                self.transactions_table.setItem(row, 1, QTableWidgetItem(supplier_name))
                
                # نوع العملية
                self.transactions_table.setItem(row, 2, QTableWidgetItem(transaction.transaction_type or ""))
                
                # رقم المرجع
                self.transactions_table.setItem(row, 3, QTableWidgetItem(transaction.reference_number or ""))
                
                # المبلغ
                amount_text = f"{transaction.amount:.2f}" if transaction.amount else "0.00"
                self.transactions_table.setItem(row, 4, QTableWidgetItem(amount_text))
                
                # العملة
                self.transactions_table.setItem(row, 5, QTableWidgetItem(transaction.currency or ""))
                
                # الوصف
                self.transactions_table.setItem(row, 6, QTableWidgetItem(transaction.description or ""))
                
                # حفظ ID في البيانات
                self.transactions_table.item(row, 0).setData(Qt.UserRole, transaction.id)
            
        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحميل البيانات",
                f"حدث خطأ أثناء تحميل بيانات العمليات:\n{str(e)}"
            )
        finally:
            session.close()
    
    def add_transaction(self):
        """إضافة عملية جديدة"""
        if not self.validate_form():
            return
        
        # في التطبيق الحقيقي، ستضيف العملية إلى قاعدة البيانات
        QMessageBox.information(
            self,
            "تم الإضافة",
            "تم إضافة العملية بنجاح\n(هذه ميزة تجريبية)"
        )
        
        self.clear_form()
        self.load_data()
    
    def update_transaction(self):
        """تحديث العملية"""
        if not self.current_transaction_id or not self.validate_form():
            return
        
        # في التطبيق الحقيقي، ستحدث العملية في قاعدة البيانات
        QMessageBox.information(
            self,
            "تم التحديث",
            "تم تحديث العملية بنجاح\n(هذه ميزة تجريبية)"
        )
        
        self.clear_form()
        self.load_data()
    
    def validate_form(self):
        """التحقق من صحة البيانات"""
        if self.supplier_combo.currentData() is None:
            QMessageBox.warning(self, "خطأ", "يرجى اختيار مورد")
            return False
        
        if self.amount_edit.value() <= 0:
            QMessageBox.warning(self, "خطأ", "يرجى إدخال مبلغ صحيح")
            return False
        
        if not self.reference_number_edit.text().strip():
            QMessageBox.warning(self, "خطأ", "يرجى إدخال رقم المرجع")
            return False
        
        return True
    
    def clear_form(self):
        """مسح النموذج"""
        self.current_transaction_id = None
        self.supplier_combo.setCurrentIndex(0)
        self.transaction_type_combo.setCurrentIndex(0)
        self.transaction_date.setDate(QDate.currentDate())
        self.reference_number_edit.clear()
        self.amount_edit.setValue(0.00)
        self.currency_combo.setCurrentIndex(0)
        self.exchange_rate_edit.setValue(1.00)
        self.description_edit.clear()
        
        self.add_button.setEnabled(True)
        self.update_button.setEnabled(False)
    
    def on_selection_changed(self):
        """عند تغيير التحديد في الجدول"""
        current_row = self.transactions_table.currentRow()
        if current_row >= 0:
            self.edit_selected()
    
    def edit_selected(self):
        """تعديل العملية المحددة"""
        current_row = self.transactions_table.currentRow()
        if current_row < 0:
            return
        
        # في التطبيق الحقيقي، ستحمل بيانات العملية من قاعدة البيانات
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة تعديل العمليات قيد التطوير"
        )
    
    def delete_selected(self):
        """حذف العملية المحددة"""
        current_row = self.transactions_table.currentRow()
        if current_row < 0:
            QMessageBox.warning(self, "لا يوجد تحديد", "يرجى تحديد عملية للحذف")
            return
        
        reply = QMessageBox.question(
            self,
            "تأكيد الحذف",
            "هل تريد حذف هذه العملية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            # في التطبيق الحقيقي، ستحذف العملية من قاعدة البيانات
            QMessageBox.information(
                self,
                "تم الحذف",
                "تم حذف العملية بنجاح\n(هذه ميزة تجريبية)"
            )
            self.load_data()
            self.clear_form()
    
    def filter_by_supplier(self):
        """تصفية العمليات حسب المورد"""
        QMessageBox.information(
            self,
            "قيد التطوير",
            "ميزة التصفية قيد التطوير"
        )
