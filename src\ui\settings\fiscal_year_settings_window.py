# -*- coding: utf-8 -*-
"""
نافذة إعدادات السنة المالية
Fiscal Year Settings Window
"""

from PySide6.QtWidgets import (QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                               QLabel, QPushButton, QFrame, QMessageBox, 
                               QMenuBar, QMenu, QAction, QToolBar)
from PySide6.QtCore import Qt, Signal
from PySide6.QtGui import QFont, QIcon

from .fiscal_year_settings import FiscalYearSettingsWidget

class FiscalYearSettingsWindow(QMainWindow):
    """نافذة إعدادات السنة المالية"""
    
    settings_saved = Signal()
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowTitle("إعدادات السنة المالية - نظام إدارة الشحنات")
        self.setMinimumSize(800, 600)
        self.resize(1000, 700)
        
        # إعداد الواجهة
        self.setup_ui()
        self.setup_menu_bar()
        self.setup_toolbar()
    
    def setup_ui(self):
        """إعداد واجهة المستخدم"""
        
        # الويدجت المركزي
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # التخطيط الرئيسي
        main_layout = QVBoxLayout(central_widget)
        main_layout.setContentsMargins(10, 10, 10, 10)
        
        # عنوان النافذة
        title_label = QLabel("إعدادات السنة المالية")
        title_label.setAlignment(Qt.AlignCenter)
        title_font = QFont()
        title_font.setPointSize(16)
        title_font.setBold(True)
        title_label.setFont(title_font)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 15px;
                background-color: #ecf0f1;
                border-radius: 8px;
                margin-bottom: 10px;
            }
        """)
        main_layout.addWidget(title_label)
        
        # ويدجت إعدادات السنة المالية
        self.fiscal_year_settings_widget = FiscalYearSettingsWidget()
        main_layout.addWidget(self.fiscal_year_settings_widget)
        
        # أزرار التحكم
        buttons_layout = QHBoxLayout()
        buttons_layout.addStretch()
        
        save_button = QPushButton("حفظ الإعدادات")
        save_button.clicked.connect(self.save_settings)
        save_button.setStyleSheet("""
            QPushButton {
                background-color: #27ae60;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #229954;
            }
        """)
        
        cancel_button = QPushButton("إلغاء")
        cancel_button.clicked.connect(self.close)
        cancel_button.setStyleSheet("""
            QPushButton {
                background-color: #e74c3c;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #c0392b;
            }
        """)
        
        apply_button = QPushButton("تطبيق")
        apply_button.clicked.connect(self.apply_settings)
        apply_button.setStyleSheet("""
            QPushButton {
                background-color: #3498db;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #2980b9;
            }
        """)
        
        buttons_layout.addWidget(save_button)
        buttons_layout.addWidget(apply_button)
        buttons_layout.addWidget(cancel_button)
        
        main_layout.addLayout(buttons_layout)
    
    def setup_menu_bar(self):
        """إعداد شريط القوائم"""
        
        menubar = self.menuBar()
        
        # قائمة ملف
        file_menu = menubar.addMenu("ملف")
        
        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_settings)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        close_action = QAction("إغلاق", self)
        close_action.setShortcut("Ctrl+W")
        close_action.triggered.connect(self.close)
        file_menu.addAction(close_action)
        
        # قائمة تحرير
        edit_menu = menubar.addMenu("تحرير")
        
        reset_action = QAction("إعادة تعيين", self)
        reset_action.triggered.connect(self.reset_settings)
        edit_menu.addAction(reset_action)
        
        # قائمة السنة المالية
        fiscal_menu = menubar.addMenu("السنة المالية")
        
        new_year_action = QAction("سنة مالية جديدة", self)
        new_year_action.triggered.connect(self.create_new_fiscal_year)
        fiscal_menu.addAction(new_year_action)
        
        close_year_action = QAction("إقفال السنة المالية", self)
        close_year_action.triggered.connect(self.close_fiscal_year)
        fiscal_menu.addAction(close_year_action)
        
        # قائمة مساعدة
        help_menu = menubar.addMenu("مساعدة")
        
        about_action = QAction("حول", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_toolbar(self):
        """إعداد شريط الأدوات"""
        
        toolbar = self.addToolBar("الأدوات الرئيسية")
        toolbar.setToolButtonStyle(Qt.ToolButtonTextBesideIcon)
        
        # زر الحفظ
        save_action = QAction("حفظ", self)
        save_action.setShortcut("Ctrl+S")
        save_action.triggered.connect(self.save_settings)
        toolbar.addAction(save_action)
        
        toolbar.addSeparator()
        
        # زر التطبيق
        apply_action = QAction("تطبيق", self)
        apply_action.triggered.connect(self.apply_settings)
        toolbar.addAction(apply_action)
        
        # زر الإعادة
        reset_action = QAction("إعادة تعيين", self)
        reset_action.triggered.connect(self.reset_settings)
        toolbar.addAction(reset_action)
        
        toolbar.addSeparator()
        
        # زر سنة مالية جديدة
        new_year_action = QAction("سنة جديدة", self)
        new_year_action.triggered.connect(self.create_new_fiscal_year)
        toolbar.addAction(new_year_action)
        
        # زر إقفال السنة
        close_year_action = QAction("إقفال السنة", self)
        close_year_action.triggered.connect(self.close_fiscal_year)
        toolbar.addAction(close_year_action)
        
        toolbar.addSeparator()
        
        # زر الإغلاق
        close_action = QAction("إغلاق", self)
        close_action.triggered.connect(self.close)
        toolbar.addAction(close_action)
    
    def save_settings(self):
        """حفظ الإعدادات"""
        try:
            if hasattr(self.fiscal_year_settings_widget, 'save_settings'):
                self.fiscal_year_settings_widget.save_settings()
            
            QMessageBox.information(
                self,
                "تم الحفظ",
                "تم حفظ إعدادات السنة المالية بنجاح"
            )
            
            self.settings_saved.emit()
            self.close()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في الحفظ",
                f"حدث خطأ أثناء حفظ إعدادات السنة المالية:\n{str(e)}"
            )
    
    def apply_settings(self):
        """تطبيق الإعدادات دون إغلاق النافذة"""
        try:
            if hasattr(self.fiscal_year_settings_widget, 'save_settings'):
                self.fiscal_year_settings_widget.save_settings()
            
            QMessageBox.information(
                self,
                "تم التطبيق",
                "تم تطبيق إعدادات السنة المالية بنجاح"
            )
            
            self.settings_saved.emit()
            
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في التطبيق",
                f"حدث خطأ أثناء تطبيق إعدادات السنة المالية:\n{str(e)}"
            )
    
    def reset_settings(self):
        """إعادة تعيين الإعدادات"""
        reply = QMessageBox.question(
            self,
            "إعادة تعيين الإعدادات",
            "هل تريد إعادة تعيين جميع إعدادات السنة المالية إلى القيم الافتراضية؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                if hasattr(self.fiscal_year_settings_widget, 'reset_settings'):
                    self.fiscal_year_settings_widget.reset_settings()
                
                QMessageBox.information(
                    self,
                    "تم إعادة التعيين",
                    "تم إعادة تعيين إعدادات السنة المالية إلى القيم الافتراضية"
                )
                
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ في إعادة التعيين",
                    f"حدث خطأ أثناء إعادة تعيين إعدادات السنة المالية:\n{str(e)}"
                )
    
    def create_new_fiscal_year(self):
        """إنشاء سنة مالية جديدة"""
        reply = QMessageBox.question(
            self,
            "سنة مالية جديدة",
            "هل تريد إنشاء سنة مالية جديدة؟\nسيتم إقفال السنة المالية الحالية.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                if hasattr(self.fiscal_year_settings_widget, 'create_new_fiscal_year'):
                    self.fiscal_year_settings_widget.create_new_fiscal_year()
                
                QMessageBox.information(
                    self,
                    "تم الإنشاء",
                    "تم إنشاء سنة مالية جديدة بنجاح"
                )
                
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ في الإنشاء",
                    f"حدث خطأ أثناء إنشاء السنة المالية الجديدة:\n{str(e)}"
                )
    
    def close_fiscal_year(self):
        """إقفال السنة المالية"""
        reply = QMessageBox.question(
            self,
            "إقفال السنة المالية",
            "هل تريد إقفال السنة المالية الحالية؟\nلا يمكن التراجع عن هذا الإجراء.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            try:
                if hasattr(self.fiscal_year_settings_widget, 'close_fiscal_year'):
                    self.fiscal_year_settings_widget.close_fiscal_year()
                
                QMessageBox.information(
                    self,
                    "تم الإقفال",
                    "تم إقفال السنة المالية بنجاح"
                )
                
            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ في الإقفال",
                    f"حدث خطأ أثناء إقفال السنة المالية:\n{str(e)}"
                )
    
    def show_about(self):
        """عرض معلومات حول النافذة"""
        QMessageBox.about(
            self,
            "حول إعدادات السنة المالية",
            "نافذة إعدادات السنة المالية\n"
            "نظام إدارة الشحنات المتقدم\n"
            "الإصدار 1.0"
        )
    
    def closeEvent(self, event):
        """عند إغلاق النافذة"""
        event.accept()
    
    def refresh_data(self):
        """تحديث البيانات"""
        if hasattr(self.fiscal_year_settings_widget, 'load_settings'):
            self.fiscal_year_settings_widget.load_settings()
